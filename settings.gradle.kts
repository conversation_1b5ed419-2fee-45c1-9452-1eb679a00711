pluginManagement {
    repositories {
        maven("https://nexus20.tclking.com/repository/proxy-nexus-maven-pub").isAllowInsecureProtocol = true
        maven("http://10.92.35.98:8081/nexus/repository/maven-public/").isAllowInsecureProtocol = true
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven("https://nexus20.tclking.com/repository/proxy-nexus-maven-pub").isAllowInsecureProtocol = true
        maven("http://10.92.35.98:8081/nexus/repository/maven-public/").isAllowInsecureProtocol = true
    }
}

rootProject.name = "Note"
include(":app")
include(":module-dashboard")
include(":module-base")
include(":module-handwritingText")
include(":module-summary")
include(":module-handwritingToText")
include (":sdk:lib-account")
//include (":sdk:lib-assistant")
include (":sdk:lib-beautify")
include (":sdk:lib-sunia")
include (":sdk:lib-sunia-auth")
include (":sdk:lib-sunia-estimate")
include (":sdk:lib-sunia-single")
include (":sdk:lib-sunia-recognize")
include(":module-helpwriting")
include(":module-polish")
include(":module-voiceToText")
include(":module-sunia")
include(":module-journal")
include(":module-journal")
include(":module-journal:journaldashboard")
include(":module-journal:inspiration")
include(":module-journal:template")
include(":module-journal:picturetotext")
include(":module-journal:database")
include(":module-journal:resources")
include(":module-journal:base")
include(":module-journal:imageanalysisdemo")
include(":module-journal:arcsoft-facedetection")
include(":module-journal:drawboard")
