package com.tcl.ai.note.utils

import android.util.DisplayMetrics
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp


/**
 * 手机设计图宽度
 */
const val PHONE_DESIGN_WIDTH = 360f
/**
 * 手机设计图高度
 */
const val PHONE_DESIGN_HEIGHT = 780f

const val PHONE_DESIGN_STATUS_HEIGHT = 38f


/**
 * 屏幕缩放计算的核心函数
 * 封装公共逻辑，避免重复代码
 *
 * 过去版本在分屏/横竖屏拖拽到全屏时，会出现缩放比例异常。
 * 根因是 rememberScreenScale 仅以 density 为 key，并且读取的是 Resources 的 DisplayMetrics。
 * 当窗口实际可用大小（LocalWindowInfo.current.containerSize）变化时，未触发重算，导致沿用过期的 scale（例如错误的 1.09/1.09）。
 * 修复点：
 * 1) 改为依赖 rememberScreenInfo()（基于 containerSize），并将 screen 宽高像素、LocalConfiguration、density 作为 remember 的 key，确保窗口变化能触发重算；
 * 2) 多窗口/平板直接返回 1f，避免不必要缩放；
 * 3) 以 screenInfo.widthPx/heightPx 作为实际屏幕基准，配合设计图尺寸计算 scale。
 */
@Composable
private fun rememberScreenScale(
    designSize: Float,
    getScreenSize: (screenInfo: ScreenSize) -> Int
): Float {
    val inMultiWindowMode = rememberIsInMultiWindowMode()
    if (isTablet || inMultiWindowMode) return 1f // 平板或多窗口模式不缩放

    val density = LocalDensity.current.density
    val configuration = LocalConfiguration.current
    val screenInfo = rememberScreenInfo()

    // 当屏幕尺寸、方向或密度变化时，重新计算缩放比例
    return remember(screenInfo.widthPx, screenInfo.heightPx, configuration, density) {
        Logger.d("ScreenScale", "density $density, screen: ${screenInfo.width}x${screenInfo.height} dp")
        getScreenSize(screenInfo) / designSize / density
    }
}

/**
 * 根据屏幕宽度缩放的dp值
 * 优化版本：使用remember缓存计算结果，避免重复计算
 */
val Number.dpw: Dp
    @Composable
    get() {
        val widthScale = rememberScreenScale(PHONE_DESIGN_WIDTH) { it.widthPx }
        return (toFloat() * widthScale).dp
    }

/**
 * 根据屏幕高度缩放的dp值
 * 优化版本：使用remember缓存计算结果，避免重复计算
 */
val Number.dph: Dp
    @Composable
    get() {
        val heightScale = rememberScreenScale(PHONE_DESIGN_HEIGHT) { it.heightPx }
        return (toFloat() * heightScale).dp
    }

/**
 * 获取屏幕宽度缩放比例的Composable函数
 * 更高效的方式：在组件顶层调用一次，然后传递给子组件使用
 */
@Composable
fun rememberWidthScale(): Float {
    return rememberScreenScale(PHONE_DESIGN_WIDTH) { it.widthPx }
}

/**
 * 获取屏幕高度缩放比例的Composable函数
 */
@Composable
fun rememberHeightScale(): Float {
    return rememberScreenScale(PHONE_DESIGN_HEIGHT) { it.heightPx }
}

/**
 * 非Composable版本的缩放计算
 * 适用于在非Compose环境中使用
 */
fun Number.dpwNonCompose(widthScale: Float): Float {
    return toFloat() * widthScale
}

/**
 * 非Composable版本的高度缩放计算
 * 适用于在非Compose环境中使用
 */
fun Number.dphNonCompose(heightScale: Float): Float {
    return toFloat() * heightScale
}
/**
 * 按屏幕缩放的宽度
 */
@Composable
fun globalDialogWidth(): Dp {
    return 336.dpw
}

/**
 * 手机上状态栏高度适配
 */
fun getStatusBarHeightAdapter(statusBarHeight: Dp): Dp {
    if (isTablet) return statusBarHeight
    return maxOf(statusBarHeight, PHONE_DESIGN_STATUS_HEIGHT.dp)
}

/**
 * 手机上 实际的状态栏高度 26dp 和 设计图要求的状态栏高度38dp 不一样
 */
@Composable
fun getStatusBarHeightAdapter(): Dp {
//    val density = LocalDensity.current.density
    val statusBarHeight = getStatusBarHeight()
    return statusBarHeight
//    return remember(density, statusBarHeight) {
//        val statusBarHeightAdapter = getStatusBarHeightAdapter(statusBarHeight)
//        Logger.i(
//            "HomeNoteContentPanel",
//            "statusBarHeight:$statusBarHeight statusBarHeightAdapter:$statusBarHeightAdapter"
//        )
//        statusBarHeightAdapter
//    }
}