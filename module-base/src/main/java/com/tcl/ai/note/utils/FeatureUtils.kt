package com.tcl.ai.note.utils

import android.content.Context

/**
 *  author : junze.liu
 *  date : 2025-07-16 21:08
 *  description : 通过feature属性，有配置的话，则为禁用防误触功能（目前Sunrise NA OM不支持防误触，其他机型暂未配置该属性）
 */

object FeatureUtils {

    const val KEY = "vendor.software.note.disable.hand.drawn.mode"
    const val KEY_FRIDA = "tcl.note.feature.reduced" // 阉割版的定制版本，如Frida

    fun hasSystemFeature(context: Context, key:String):Boolean {
        val result = context.packageManager.hasSystemFeature(key)
        return result
    }

    fun isDisableHandDrawnMode(context: Context):Bo<PERSON><PERSON>{
        //是否有配置feature属性，有配置的话，则为禁用防误触功能
        val disable = context.packageManager.hasSystemFeature(KEY)
        Logger.i("FeatureUtils","isDisableHandDrawnMod, disable:$disable")
        return disable
    }


}