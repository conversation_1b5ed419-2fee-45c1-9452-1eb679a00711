plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
}
val standaloneSummary = project.findProperty("standalonePrediceEngine")?.toString()?.toBoolean() ?: false
android {
    namespace = "com.tcl.ai.note.predengine"
    compileSdk = libs.versions.compileSdk.get().toIntOrNull() ?: 35

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toIntOrNull() ?: 34

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")

        externalNativeBuild {
            cmake {
                cppFlags += ""
//                arguments += listOf(
//                    "-DANDROID_TOOLCHAIN=clang",
//                    "-DANDROID_CPP_FEATURES=rtti exceptions",
//                    "-DCMAKE_CXX_FLAGS=-std=c++11 -frtti -fexceptions -finput-charset=UTF-8"
//                )
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    buildFeatures {
        buildConfig = true
    }

    flavorDimensions.add("device")
    productFlavors {
        create("phone") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "true")
        }
        create("tablet") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "false")
        }
    }

    sourceSets {
        getByName("phone").java.srcDirs("src/inner/java")
        getByName("tablet").java.srcDirs("src/inner/java")
    }

    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.18.1"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.startup)
}