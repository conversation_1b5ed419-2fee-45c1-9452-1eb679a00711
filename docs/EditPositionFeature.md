# 笔记页面查看位置记忆功能（简化版）

## 功能概述

该功能实现了记住每条Note从详情页EditActivity退出时的页面显示位置，下次从列表页进入后能自动定位到上次查看的地方。

**简化设计原则**：
- 只记住Y轴滚动位置和缩放大小
- 不记录X轴偏移（保持居中）
- 不记录光标位置（加载时为预览态）
- 专注于页面显示位置的恢复

## 实现原理

### 数据存储
在Note实体中新增2个字段：
```kotlin
var lastViewOffsetY: Float = 0f,        // 最后查看时的Y轴偏移（页面滚动位置）
var lastViewScale: Float = 1.0f         // 最后查看时的缩放比例
```

### 保存时机
1. **手势缩放/滚动结束时** - 在`onScrollAndScaleEnd`中自动保存
2. **Activity生命周期** - 在`EditActivity.onStop()`中保存

### 恢复时机
在笔记内容加载完成后（`onCanvasHandleFinish`回调中）自动恢复到上次查看位置

## 核心组件

### 1. 数据库层
- **NoteEntity.kt**: 添加新字段定义
- **NoteDatabase.kt**: 在MIGRATION_1_2中添加字段
- **NoteDao.kt**: 添加`updateLastEditPosition`方法
- **NoteRepository2.kt**: 添加位置更新接口

### 2. ViewModel层
- **RichTextViewModel2.kt**: 
  - `saveLastEditPosition()`: 保存编辑位置
  - `getLastEditPosition()`: 获取最后编辑位置
  - `getLastEditCursorPosition()`: 获取最后光标位置

### 3. UI层
- **TextAndDrawBoardLayout.kt**:
  - `saveCurrentEditPosition()`: 保存当前编辑位置
  - `restoreLastEditPosition()`: 恢复上次编辑位置
  - 在缩放结束和加载完成时调用相应方法

- **EditActivity.kt**:
  - 在`onStop()`中保存当前编辑状态

## 使用流程

### 保存流程
```
用户操作（滚动/缩放/编辑） 
    ↓
UI层捕获位置变化
    ↓
调用saveCurrentEditPosition()
    ↓
ViewModel.saveLastEditPosition()
    ↓
Repository.updateLastEditPosition()
    ↓
数据库更新
```

### 恢复流程
```
用户进入编辑页
    ↓
加载笔记内容
    ↓
内容加载完成回调
    ↓
调用restoreLastEditPosition()
    ↓
从数据库读取上次位置
    ↓
恢复滚动位置和缩放
    ↓
恢复光标位置
```

## 关键特性

### 1. 完整的位置信息
- **滚动位置**: 记住用户滚动到的具体位置
- **缩放大小**: 记住用户设置的缩放比例
- **光标位置**: 记住文本编辑的光标位置

### 2. 智能保存策略
- 防抖机制避免频繁保存
- 只在有效变化时保存
- 异常处理确保稳定性

### 3. 精确恢复
- 使用`updateScaleInfo`确保缩放和位置同步恢复
- 延迟恢复确保UI完全加载
- 防重复恢复机制

### 4. 容错处理
- 完善的异常处理和日志记录
- 边界值检查和默认值处理
- 兼容性处理确保老数据正常工作

## 测试验证

提供了完整的单元测试：
- 基本的保存和恢复功能测试
- 不同缩放比例的测试
- 边界值处理测试
- 默认值验证测试

## 性能考虑

1. **数据库操作**: 异步执行，不阻塞UI
2. **内存使用**: 复用MatrixInfo对象，避免频繁创建
3. **保存频率**: 合理的保存时机，避免过度保存
4. **恢复延迟**: 适当延迟确保UI稳定性

## 兼容性

- 向后兼容：老数据自动使用默认值
- 数据库迁移：在现有迁移中添加字段
- 异常处理：确保功能异常不影响主流程
