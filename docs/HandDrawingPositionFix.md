# 手绘改动导致位置恢复失败的修复方案

## 问题分析

当笔记包含手绘内容时，位置恢复会失败，原因如下：

1. **初始化覆盖**：`implViewModelFunc`中的`implChangeScaleFunc`在初始化时被调用
2. **默认值传入**：此时传入的是默认的`MatrixInfo(0, 0, 1.0)`
3. **覆盖恢复值**：这会覆盖我们从数据库恢复的位置信息
4. **保存默认值**：`onScrollAndScaleEnd`中保存的就是被覆盖后的默认值
5. **下次失效**：下次打开时获取到的就是默认值，恢复失败

## 修复策略

### 1. 早期保护机制
- 在类初始化时就设置保护标志：`isRestoringPosition = true`, `shouldSkipSave = true`
- 防止初始化过程中的任何保存操作

### 2. 智能跳过逻辑
```kotlin
implChangeScaleFunc = { matrixInfo ->
    val isNewNote = richTextViewModel.uiState.value.isNewNote
    if (isNewNote || !isRestoringPosition) {
        offsetAndScaleHandler.updateScaleInfo(matrixInfo)
    } else {
        Logger.d(TAG, "Skipping updateScaleInfo during position restore")
    }
}
```

### 3. 多重检查机制
- `checkAndRestorePosition()`: 检查是否需要恢复位置
- 区分新笔记和现有笔记的处理逻辑
- 只对现有笔记进行位置恢复

### 4. 延迟恢复
- 在`onCanvasHandleFinish`中延迟500ms再恢复
- 确保所有初始化完成后再进行恢复

## 关键日志

### 正常工作时应该看到：
```
onAttachedToWindow: noteId=123, isNewNote=false, hasRestoredPosition=false
Preloading position data for existing note
implChangeScaleFunc called with: MatrixInfo(0.0, 0.0, 1.0), isRestoringPosition=true
Skipping updateScaleInfo during position restore for existing note
onCanvasHandleFinish - checking and attempting to restore view position
Raw DB query result: Raw DB: noteId=123, offsetX=100.0, offsetY=-200.0, scale=1.5
Retrieved from DB: MatrixInfo(offsetX=100.0, offsetY=-200.0, scale=1.5)
Restoring view position: MatrixInfo(offsetX=100.0, offsetY=-200.0, scale=1.5)
✅ View position restored successfully and verified!
Restore flags cleared, saving is now enabled
```

### 问题仍存在时会看到：
```
Raw DB query result: Raw DB: noteId=123, offsetX=0.0, offsetY=0.0, scale=1.0
Retrieved from DB: MatrixInfo(offsetX=0.0, offsetY=0.0, scale=1.0)
No valid view position to restore - using defaults
```

## 调试步骤

1. **检查保护机制**：
   - 查看是否有 "Skipping updateScaleInfo during position restore" 日志
   - 确认 `isRestoringPosition=true` 在初始化时生效

2. **检查数据库数据**：
   - 查看 "Raw DB query result" 日志
   - 确认数据库中确实保存了非默认值

3. **检查恢复时机**：
   - 确认 `checkAndRestorePosition` 被正确调用
   - 确认延迟恢复在正确时机执行

4. **检查笔记类型**：
   - 确认 `isNewNote=false` 对于现有笔记
   - 新笔记应该跳过恢复逻辑

## 手动测试方法

1. **创建测试笔记**：
   - 创建一个包含手绘内容的笔记
   - 滚动到特定位置并缩放
   - 退出应用

2. **验证保存**：
   - 检查数据库中的 `lastViewOffsetX`, `lastViewOffsetY`, `lastViewScale` 字段
   - 确认保存了非默认值

3. **验证恢复**：
   - 重新打开应用
   - 进入该笔记
   - 检查是否恢复到之前的位置和缩放

## 强制恢复方法

如果自动恢复失败，可以调用：
```kotlin
textAndDrawBoardLayout.forceRestorePosition()
```

这会重置所有标志并强制尝试恢复位置。
