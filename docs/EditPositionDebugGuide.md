# 编辑位置恢复功能调试指南

## 问题现象
数据已经正确保存到数据库中，也能正确读取，但是重新打开后页面没有按照数据库中存储的位置和缩放信息进行回显。

## 调试步骤

### 1. 检查日志输出
运行应用后，在Logcat中搜索以下关键字：
- `TextAndDrawBoard` - 主要的恢复逻辑
- `RichTextViewModel2` - 数据读取逻辑
- `saveLastEditPosition` - 保存位置
- `getLastEditPosition` - 读取位置
- `restoreLastEditPosition` - 恢复位置

### 2. 关键日志点

#### 保存时的日志：
```
Saving edit position: noteId=X, offsetX=X, offsetY=X, scale=X, cursor=X
Successfully saved edit position
```

#### 读取时的日志：
```
getLastEditPosition: noteId=X, matrix=MatrixInfo(offsetX=X, offsetY=X, scale=X)
getLastEditCursorPosition: noteId=X, cursor=X
```

#### 恢复时的日志：
```
Starting restore process for noteId: X
Retrieved from DB: matrix=MatrixInfo(...), cursor=X
Restore conditions: hasValidPosition=true, hasValidCursor=true, shouldRestore=true
Before restore - current matrix: offsetX=X, offsetY=X, scale=X
After restore - current matrix: offsetX=X, offsetY=X, scale=X
Successfully restored edit position and scale
```

### 3. 常见问题排查

#### 问题1：恢复条件不满足
检查日志中的 `Restore conditions` 行：
- `hasValidPosition=false` - 说明位置数据为默认值
- `hasValidCursor=false` - 说明光标位置为0
- `shouldRestore=false` - 说明没有满足恢复条件

**解决方案**：检查保存逻辑是否正确执行

#### 问题2：恢复时机过早
检查日志中是否有：
```
Skip restore: noteId=X, hasRestoredPosition=true
```
或者
```
Layout changed and not loading, attempting backup restore
```

**解决方案**：确保在正确的时机调用恢复

#### 问题3：UI状态不同步
检查 `Before restore` 和 `After restore` 的矩阵信息是否发生变化。

**解决方案**：确保所有相关组件都正确更新

### 4. 手动验证步骤

1. **保存测试**：
   - 打开一个笔记
   - 滚动到某个位置
   - 缩放到非1.0的比例
   - 退出应用
   - 检查数据库中的 `lastEditOffsetX`, `lastEditOffsetY`, `lastEditScale` 字段

2. **恢复测试**：
   - 重新打开应用
   - 进入同一个笔记
   - 观察是否恢复到之前的位置和缩放

### 5. 调试代码

可以在 `restoreLastEditPosition` 方法中添加临时调试代码：

```kotlin
// 在恢复前强制设置一个测试值
val testMatrix = MatrixInfo(offsetX = 100f, offsetY = -200f, scale = 1.5f)
offsetAndScaleHandler.updateScaleInfo(testMatrix)
```

### 6. 可能的解决方案

#### 方案1：增加延迟时间
如果恢复时机过早，可以增加延迟：
```kotlin
delay(1000) // 增加到1秒
```

#### 方案2：强制刷新UI
在恢复后强制刷新：
```kotlin
invalidate()
requestLayout()
```

#### 方案3：检查OffsetAndScaleHandler状态
确保 `OffsetAndScaleHandler` 已经正确初始化并设置了边界。

### 7. 验证数据库
可以通过数据库查看工具检查数据是否正确保存：
```sql
SELECT noteId, lastEditOffsetX, lastEditOffsetY, lastEditScale, lastEditCursorPosition 
FROM notes 
WHERE noteId = [你的笔记ID];
```

### 8. 临时解决方案
如果问题持续存在，可以尝试：
1. 在多个时机尝试恢复（已实现）
2. 使用 `post` 延迟执行恢复逻辑
3. 监听特定的UI状态变化来触发恢复

## 预期行为
正常情况下，应该看到以下完整的日志流程：
1. 保存位置的日志
2. 读取位置的日志
3. 恢复位置的详细过程日志
4. 恢复前后的状态对比
5. 成功恢复的确认日志

如果任何一步缺失或异常，都可能导致恢复失败。
