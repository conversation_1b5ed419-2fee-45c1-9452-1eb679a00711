package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.semantics.hideFromAccessibility
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.utils.rememberOriginalSize


@Composable
internal fun HandDrawnImageThumbnailSize(
    note: HomeNoteItemModel,
    thumbnailSize: DpSize
) {

    val (handwritingThumbnail, imageCacheKey) = getImageUrlAndCacheKey(note)
    val isImageType = note.isOnlyImageType
    val originalImageSize = rememberOriginalSize()
    HandDrawnImageThumbnail(
        thumbnailSize = thumbnailSize,
        originalImageSize = originalImageSize
    ) {
        HomeRichTextPreview(
            note = note,
            modifier = Modifier
                .fillMaxSize(),
            isNormalMode = false
        )
        // 显示图片
        HomeAsyncImage(
            modifier = Modifier.fillMaxSize(),
            data = handwritingThumbnail,
            imageCacheKey = imageCacheKey,
            contentScale = if (isImageType) ContentScale.Fit else ContentScale.Crop,
            contentDescription = null
        )
    }
}


/**
 * 手绘笔记图片缩略图组件
 */
@Composable
fun HandDrawnImageThumbnail(
    modifier: Modifier = Modifier,
    thumbnailSize: DpSize = DpSize(109.dp, 200.dp), // 缩略图尺寸
    originalImageSize: DpSize = DpSize(360.dp, 800.dp), // 1K手机尺寸
    content: @Composable () -> Unit
) {

    Box(
        modifier = Modifier
            .size(thumbnailSize)
            .clipToBounds()
            .semantics {
                hideFromAccessibility()
            } // 裁剪超出缩略图边界的内容
    ) {
        BoxWithConstraints(
            modifier = Modifier
                .semantics {
                    hideFromAccessibility()
                }
        ) {
            // 计算缩放比例
            val scaleX = maxWidth / originalImageSize.width
            val scaleY = maxHeight / originalImageSize.height

            // 使用较小的缩放比例以确保图片完全显示（等比例缩放）
//                val scale = minOf(scaleX, scaleY)
            Box(
                modifier = Modifier
                    .requiredSize(originalImageSize)
                    .semantics {
                        hideFromAccessibility()
                    } // 保持原始图片尺寸
                    .graphicsLayer {
                        // 应用缩放
                        this.scaleX = scaleX
                        this.scaleY = scaleY
                        val wdPx = originalImageSize.width.toPx()
                        val maxPX = maxWidth.toPx()

                        // 设置变换原点为左上角，确保左对齐
                        transformOrigin = TransformOrigin(0.5f, 0.5f)
                        this.translationX = -(maxPX - wdPx * scaleX) / 2
                        translationY = 0f
                    }
                    .clipToBounds() // 再次裁剪，确保不超出边界
            ) {
                content()
            }
        }
    }
}

@Composable
fun HandDrawnImageThumbnail(
    modifier: Modifier = Modifier,
    thumbnailSize: DpSize = DpSize(109.dp, 200.dp), // 直接使用UI图的固定尺寸
    content: @Composable () -> Unit
) {
    val originalImageSize = rememberOriginalSize()
    BoxWithConstraints(
        modifier = modifier
            .size(thumbnailSize)
            .semantics {
                hideFromAccessibility()
            }
            .clipToBounds()
    ) {
        val scale = minOf(
            maxWidth / originalImageSize.width,
            maxHeight / originalImageSize.height
        )

        Box(
            modifier = Modifier
                .requiredSize(originalImageSize)
                .graphicsLayer {
                    this.scaleX = scale
                    this.scaleY = scale
                    val wdPx = originalImageSize.height.toPx()
                    val maxPX = maxHeight.toPx()
                    this.translationY = -(maxPX - wdPx * scale) / 2
                    // 关键：设置变换原点为左上角 (0,0)
                    transformOrigin = TransformOrigin(0.5f, 0.5f)
                    // 不需要 translation，让内容自然从左上角开始
                    translationX = 0f
//                    translationY = 0f
                }
                .clipToBounds()
        ) {
            content()
        }
    }
}