package com.tcl.ai.note.home.utils

import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.FirstScreenThumbnailType
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.home.model.NoteContentInfo
import com.tcl.ai.note.home.model.NoteDisplayInfo
import com.tcl.ai.note.home.model.ThumbnailType
import com.tcl.ai.note.utils.Logger

/**
 * 分析笔记内容信息
 */
fun analyzeNoteContent(item: NoteListItem): NoteContentInfo {
    val cleanContent = cleanContentFontLineBreak(item.summary)
    return NoteContentInfo(
        hasTitle = item.title.isNotBlank(),
        hasContent = item.content?.isNotBlank() == true,
        hasImage = !item.firstPicture.isNullOrEmpty(),
        hasHandwriting = !item.handwritingThumbnail.isNullOrEmpty(),
        hasAudio = item.hasAudio == true,
        cleanContent = cleanContent
    )
}

/**
 * 根据需求规则获取笔记显示信息 - 支持 NoteListItem
 */
fun getNoteDisplayInfo(item: NoteListItem, formattedDate: String, firstScreenThumbnailType: FirstScreenThumbnailType?): NoteDisplayInfo {
    val contentInfo = analyzeNoteContent(item)
    Logger.d("HomeNoteViewModel", "firstScreenThumbnailType: $firstScreenThumbnailType")
    return if (contentInfo.hasTitle) {
        // 1. 有标题的情况
        when {
            // 1.1 & 1.6 有正文且无图片/手绘 - 显示正文内容
            contentInfo.hasContent && !contentInfo.hasImage && !contentInfo.hasHandwriting -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.PURE_TEXT,
                primaryTitle = item.title,
                secondaryInfo = formattedDate,
                showAudioIcon = contentInfo.hasAudio
            )

            // 1.7-1.9 有正文且有图片/手绘 - 根据富文本和手绘情况决定显示模式
            contentInfo.hasContent -> {
                when {
                    // 混合模式：且有手绘
                     contentInfo.hasHandwriting -> NoteDisplayInfo(
                         thumbnailType = ThumbnailType.MIXED_CONTENT,
                         primaryTitle = item.title,
                         secondaryInfo = formattedDate,
                         showAudioIcon = contentInfo.hasAudio
                     )
                    // 纯富文本模式：有富文本样式但无手绘
                    !contentInfo.hasHandwriting -> NoteDisplayInfo(
                        thumbnailType = ThumbnailType.PURE_TEXT,
                        primaryTitle = item.title,
                        secondaryInfo = formattedDate,
                        showAudioIcon = contentInfo.hasAudio
                    )
                    // 第一屏模式：其他情况（有图片/手绘但无富文本样式）
                    else -> NoteDisplayInfo(
                        thumbnailType = ThumbnailType.FIRST_SCREEN,
                        primaryTitle = item.title,
                        secondaryInfo = formattedDate,
                        showAudioIcon = contentInfo.hasAudio
                    )
                }
            }

            // 1.2 无正文且无其他内容 - 显示A图标
            !contentInfo.hasImage && !contentInfo.hasHandwriting && !contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.TEXT_ICON,
                primaryTitle = item.title,
                secondaryInfo = formattedDate,
                showAudioIcon = false
            )

            // 1.3 只有录音 - 显示录音空态图标
            !contentInfo.hasImage && !contentInfo.hasHandwriting && contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.AUDIO_ICON,
                primaryTitle = item.title,
                secondaryInfo = formattedDate,
                showAudioIcon = true
            )

            // 1.4 只有手绘 - 显示第一屏内容
            !contentInfo.hasImage && contentInfo.hasHandwriting && !contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.FIRST_SCREEN,
                primaryTitle = item.title,
                secondaryInfo = formattedDate,
                showAudioIcon = false
            )

            // 1.5 只有图片 - 显示第一屏内容
            contentInfo.hasImage && !contentInfo.hasHandwriting && !contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.FIRST_SCREEN,
                primaryTitle = item.title,
                secondaryInfo = formattedDate,
                showAudioIcon = false
            )

            // 1.10-1.13 其他情况（图片/手绘/录音的组合） - 显示第一屏内容
            else -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.FIRST_SCREEN,
                primaryTitle = item.title,
                secondaryInfo = formattedDate,
                showAudioIcon = contentInfo.hasAudio
            )
        }
    } else {
        // 2. 无标题的情况
        when {
            // 2.1 & 2.6 有正文且无图片/手绘 - 显示正文，标题显示正文第一行
            contentInfo.hasContent && !contentInfo.hasImage && !contentInfo.hasHandwriting -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.PURE_TEXT,
                primaryTitle = getFirstLineOfContent(contentInfo.cleanContent),
                secondaryInfo = formattedDate,
                showAudioIcon = contentInfo.hasAudio
            )

            // 2.7-2.9 有正文且有图片/手绘 - 根据富文本和手绘情况决定显示模式
            contentInfo.hasContent -> {
                when {
                    // 混合模式：且有手绘
                     contentInfo.hasHandwriting -> NoteDisplayInfo(
                         thumbnailType = ThumbnailType.MIXED_CONTENT,
                         primaryTitle = getFirstLineOfContent(contentInfo.cleanContent),
                         secondaryInfo = formattedDate,
                         showAudioIcon = contentInfo.hasAudio
                     )
                    // 纯富文本模式：有富文本样式但无手绘
                    !contentInfo.hasHandwriting -> NoteDisplayInfo(
                        thumbnailType = ThumbnailType.PURE_TEXT,
                        primaryTitle = getFirstLineOfContent(contentInfo.cleanContent),
                        secondaryInfo = formattedDate,
                        showAudioIcon = contentInfo.hasAudio
                    )
                    // 第一屏模式：其他情况（有图片/手绘但无富文本样式）
                    else -> NoteDisplayInfo(
                        thumbnailType = ThumbnailType.FIRST_SCREEN,
                        primaryTitle = getFirstLineOfContent(contentInfo.cleanContent),
                        secondaryInfo = formattedDate,
                        showAudioIcon = contentInfo.hasAudio
                    )
                }
            }

            // 2.2 无正文且无其他内容 - 这种情况不应该在首页显示
            !contentInfo.hasImage && !contentInfo.hasHandwriting && !contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.UNKNOWN,
                secondaryInfo = formattedDate,
                showAudioIcon = false
            )

            // 2.3 只有录音 - 显示录音空态图标
            !contentInfo.hasImage && !contentInfo.hasHandwriting && contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.AUDIO_ICON,
                primaryTitleResId = R.string.audio_title,
                secondaryInfo = formattedDate,
                showAudioIcon = true
            )
            // 2.4 只有手绘 - 显示第一屏内容
            !contentInfo.hasImage && contentInfo.hasHandwriting && !contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.FIRST_SCREEN,
                primaryTitleResId = R.string.handwritten_title,
                secondaryInfo = formattedDate,
                showAudioIcon = false
            )

            // 2.5 只有图片 - 显示第一屏内容
            contentInfo.hasImage && !contentInfo.hasHandwriting && !contentInfo.hasAudio -> NoteDisplayInfo(
                thumbnailType = ThumbnailType.FIRST_SCREEN,
                primaryTitleResId = R.string.image_title,
                secondaryInfo = formattedDate,
                showAudioIcon = false
            )

            // 2.10-2.13 其他情况（图片/手绘/录音的组合） - 显示第一屏内容
            else -> {
                val titleResId = when {
                    // 优先级：手绘 > 录音 > 图片
                    contentInfo.hasHandwriting -> if (firstScreenThumbnailType == FirstScreenThumbnailType.IMAGE) {
                        if (contentInfo.hasAudio) R.string.audio_title else {
                            R.string.image_title
                        }
                    } else {
                        R.string.handwritten_title
                    }
                    else -> R.string.note // 使用通用的"笔记"资源
                }
                NoteDisplayInfo(
                    thumbnailType = ThumbnailType.FIRST_SCREEN,
                    primaryTitleResId = titleResId,
                    secondaryInfo = formattedDate,
                    showAudioIcon = contentInfo.hasAudio
                )
            }
        }
    }
}

/**
 * 获取内容的第一行
 */
fun getFirstLineOfContent(content: String): String {
    return content.split("\n").firstOrNull()?.trim() ?: ""
}


/**
 * 只清除前面的换行符
 */
fun cleanContentFontLineBreak(content: String?): String {
    if (content.isNullOrBlank()) return ""

    // 只清除前面的换行符，保留其他格式
    return content.trimStart('\n', '\r')
}