package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.hideFromAccessibility
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.utils.rememberThumbnailSize
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.rememberScreenInfo
/**
 * 混合模式：底部富文本，上层叠加手绘
 */
@Composable
internal fun MixContent(note: HomeNoteItemModel) {

    val contentDescription = stringResource(R.string.handwritten_title)
    val thumbnailSize = rememberThumbnailSize()
    Box {
        HandDrawnImageThumbnailSize(
            note = note,
            thumbnailSize = thumbnailSize,
        )
        //主要用于无障碍talkback 的蒙版 因为上面的缩略图是宽高全屏缩放而来的，焦点有问题
        Box(
            modifier = Modifier
                .size(thumbnailSize)
                .align(Alignment.Center)
                .semantics() {
                    contentDescription.let { this.contentDescription = it }
                })
    }
}
/**
 * 手机端竖屏  手绘显示正常
 */
@Composable
internal fun CardMixContent(note: HomeNoteItemModel) {
    val thumbnailSize= rememberThumbnailSize()
    val (handwritingThumbnail,imageCacheKey) = getImageUrlAndCacheKey(note)
    val contentDescription = stringResource(R.string.handwritten_title)
    val contentScale= getContentScale(note)
    Logger.d("CardMixContent: handwritingThumbnail: $handwritingThumbnail noteID ${note.noteId}")
    Box {
        HomeThumbnailPreview(
            targetWidth = thumbnailSize.width,
            targetHeight = thumbnailSize.height
        ) {
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .fillMaxSize(),
                isNormalMode = false
            )
            HomeAsyncImage(
                modifier = Modifier.fillMaxSize(),
                data = handwritingThumbnail,
                imageCacheKey = imageCacheKey,
                contentScale = contentScale,
                contentDescription = null
            )
        }

        //主要用于无障碍talkback 的蒙版 因为上面的缩略图是宽高全屏缩放而来的，焦点有问题
        Box(
            modifier = Modifier
                .size(thumbnailSize)
                .align(Alignment.Center)
                .semantics() {
                    contentDescription.let { this.contentDescription = it }
                })
    }
}
enum class FillMode {
    FIT,     // 等比例缩放，完全显示内容（可能有空白）
    FILL,    // 等比例缩放，填满容器（可能裁剪）
    STRETCH  // 拉伸填充（不保持比例）
}

@Composable
fun HomeThumbnailPreview(
    modifier: Modifier = Modifier,
    targetWidth: Dp = 300.dp,
    targetHeight: Dp = 500.dp,
    fillMode: FillMode = FillMode.STRETCH, // 添加填充模式选项
    content: @Composable () -> Unit
) {
    //获取屏幕宽高
    val screenInfo = rememberScreenInfo()
    Box(
        modifier = modifier
            .size(targetWidth, targetHeight)
            .semantics {
                hideFromAccessibility()
            }
    ) {
        BoxWithConstraints(
            modifier = Modifier
                .semantics {
                    hideFromAccessibility()
                }
        ) {
            val spaceHeight = 190.dp
            val originalWidth = screenInfo.width.dp
            val originalHeight = screenInfo.height.dp - spaceHeight
            val scaleX = maxWidth / originalWidth
            val scaleY = maxHeight / originalHeight

            val (finalScaleX, finalScaleY) = when (fillMode) {
                FillMode.FIT -> {
                    val scale = minOf(scaleX, scaleY)
                    scale to scale
                }

                FillMode.FILL -> {
                    val scale = maxOf(scaleX, scaleY)
                    scale to scale
                }

                FillMode.STRETCH -> scaleX to scaleY
            }
            Box(
                modifier = Modifier
                    .requiredSize(originalWidth, originalHeight)
                    .semantics {
                        hideFromAccessibility()
                    }
                    .graphicsLayer {
                        val wdPx = originalWidth.toPx()
                        val maxPX = maxWidth.toPx()
                        this.translationX = -(maxPX - wdPx * finalScaleX) / 2 - 1.dp.toPx()
                        this.scaleX = finalScaleX
                        this.scaleY = finalScaleY
                        transformOrigin = TransformOrigin.Center
                    }
                    .let {
                        if (fillMode == FillMode.FILL) it.clipToBounds() else it
                    },
            ) {
                content()
            }
        }
    }
}
