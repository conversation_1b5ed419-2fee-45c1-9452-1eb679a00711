package com.tcl.ai.note.home.screen

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import com.tcl.ai.note.home.components.categorylist.HomeCategoryDrawerContent
import com.tcl.ai.note.home.components.notelist.HomeNoteListContent
import com.tcl.ai.note.home.components.preview.TabletHomeScreenPreviewParameterProvider
import com.tcl.ai.note.home.components.preview.TabletPreviewState
import com.tcl.ai.note.home.components.base.rememberTabletLeftWeight
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.state.HomeCategoryUiState
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.action.HomeNoteListAction

/**
 * 仅做预览
 * 平板设备的主页屏幕,包含左侧边栏和右侧内容区域
 * 保持组件无状态，状态提升，在ViewModel中处理状态逻辑，单项数据流。
 * 不要在二三级组件里面引入ViewModel，通过事件来处理调用，更有利于预览和维护。
 * @param noteUiState 主页UI状态
 * @param categoryUiState 分类UI状态
 * @param onAction 主页操作回调
 * @param onCategoryAction 分类操作回调
 */
@Composable
fun TabletHomeScreen(
    noteUiState: HomeNoteUiState,
    categoryUiState: HomeCategoryUiState,
    onAction: (HomeNoteListAction) -> Unit,
    onCategoryAction: (HomeCategoryAction) -> Unit = {},
) {
    val left = rememberTabletLeftWeight()
    val right = 1f - left
    Row(
        modifier = Modifier
            .fillMaxSize()
    ) {
        // 左侧边栏
        HomeCategoryDrawerContent(
            modifier = Modifier.weight(left),
            categoryUiState = categoryUiState,
            onCategoryAction
        )
        // 右侧内容区域
        HomeNoteListContent(modifier = Modifier.weight(right), noteUiState, onAction)
    }
}



@Preview(
    showBackground = true,
    widthDp = 800,
    heightDp = 600,
    name = "平板-所有状态"
)
@Composable
fun NotesAppTabletPreview(
    @PreviewParameter(TabletHomeScreenPreviewParameterProvider::class)
    previewState: TabletPreviewState
) {
        TabletHomeScreen(
            noteUiState = previewState.homeNoteUiState,
            categoryUiState = previewState.homeCategoryUiState,
            onAction = {}
        )
}

