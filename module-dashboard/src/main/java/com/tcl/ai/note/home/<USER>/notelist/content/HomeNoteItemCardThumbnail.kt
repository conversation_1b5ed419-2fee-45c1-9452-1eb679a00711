package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.model.ThumbnailType
import com.tcl.ai.note.home.utils.rememberOriginalSize
import com.tcl.ai.note.home.utils.rememberThumbnailSize
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTabletLandscape
import com.tcl.ai.note.widget.components.BottomFadeBox

@Composable
internal fun CardTypeContent(note: HomeNoteItemModel) {
    Logger.d("HomeNoteItemCard", "thumbnailType:${note.thumbnailType}")
    when (note.thumbnailType) {
        ThumbnailType.FIRST_SCREEN -> {
            // 显示第一屏信息（图片/手绘）
            FirstScreenImageDisplay(note)
        }

        ThumbnailType.PURE_TEXT -> {
            PureText(note)
        }

        ThumbnailType.AUDIO_ICON -> {
            OnlyAudioIcon()
        }

        ThumbnailType.TEXT_ICON -> {
            OnlyTextIcon()
        }

        ThumbnailType.MIXED_CONTENT -> {
            if (isTabletLandscape) {
                MixContent(note)
            } else {
                CardMixContent(note)
            }
        }

        ThumbnailType.UNKNOWN -> {
            //未知类型
        }
    }
}

/**
 * 显示录音图标
 */
@Composable
private fun OnlyAudioIcon() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_note_only_audio),
            contentDescription = stringResource(R.string.audio_title),
            colorFilter = ColorFilter.tint(colorResource(R.color.home_note_list_icon_hint_color)),
            modifier = Modifier.size(100.dp)
        )
    }
}

/**
 * 纯文本
 */
@Composable
private fun PureText(note: HomeNoteItemModel) {
    val noteDes = note.summary ?: ""
    BottomFadeBox(
        modifier = Modifier
            .fillMaxSize()
            .semantics {
                contentDescription = noteDes
            }
            .padding(12.dp),
        fadeHeight = 24.dp
    ) {
        HomeRichTextPreview(
            note = note,
            modifier = Modifier.fillMaxSize(),
            isNormalMode = true
        )
    }
}


/**
 *  显示文本图标
 */
@Composable
private fun OnlyTextIcon() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_note_only_text),
            contentDescription = stringResource(R.string.text_speaker),
            colorFilter = ColorFilter.tint(colorResource(R.color.home_note_list_icon_hint_color)),
            modifier = Modifier.size(100.dp)
        )
    }
}

/**
 *  显示第一屏图片/手绘
 */
@Composable
private fun FirstScreenImageDisplay(note: HomeNoteItemModel) {
    // 优先级：手绘缩略图 > 首图 > image字段
    val (handwritingThumbnail, imageCacheKey) = getImageUrlAndCacheKey(note)
    val contentScale = getContentScale(note)
    Logger.d(
        "HomeNoteItemCard",
        "AsyncImageWithFallback - handwritingThumbnail: $handwritingThumbnail, date: ${note.date}"
    )

    val thumbnailSize = rememberThumbnailSize()
    val originalImageSize = rememberOriginalSize()
//    HandDrawnImageThumbnail(
//        thumbnailSize = thumbnailSize,
//        originalImageSize = originalImageSize
//    ) {
    HomeAsyncImage(
        modifier = Modifier
            .fillMaxSize(),
        data = handwritingThumbnail,
        imageCacheKey = imageCacheKey,
        contentScale = contentScale,
        contentDescription = stringResource(R.string.handwritten_title)
    )
//    }
}


