package com.tcl.ai.note.home.screen

import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.tcl.ai.note.home.components.base.HomeAdaptiveNavigationScreen
import com.tcl.ai.note.home.components.categorylist.HomeCategoryDrawerContent
import com.tcl.ai.note.home.components.notelist.HomeNoteListContent
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.home.vm.state.HomeCategoryUiState
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.utils.isTablet

/**
 * 手机设备的主页屏幕,仅做预览
 */
@Composable
fun PhoneHomeScreen(
    homeNoteUiState: HomeNoteUiState,
    categoryUiState: HomeCategoryUiState,
    onAction: (HomeNoteListAction) -> Unit,
    onCategoryAction: (HomeCategoryAction) -> Unit = { }
) {
    val rememberDrawerState = rememberDrawerState(DrawerValue.Closed)
    HomeAdaptiveNavigationScreen(
        isTablet = false,
        leftPanelWeight = 0.3f,
        drawerState = rememberDrawerState,
        gesturesEnabled = true, // 搜索模式下禁用抽屉手势
        drawerContent = {
            HomeCategoryDrawerContent(
                categoryUiState = categoryUiState,
                onCategoryAction = onCategoryAction,
                updateIconContent = {
                }
            )
        },
        content = {
            HomeNoteListContent(modifier = Modifier.fillMaxHeight(), homeNoteUiState, onAction)
        })
}

@Preview(showBackground = true)
@Composable
fun NotesAppPhonePreview() {
        PhoneHomeScreen(
            homeNoteUiState = HomeNoteUiState(),
            categoryUiState = HomeCategoryUiState(),
            onAction = {},
            onCategoryAction = {}
        )
}








