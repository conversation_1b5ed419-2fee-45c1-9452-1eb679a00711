package com.tcl.ai.note.home.components.pop

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScaleRightTop
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.home.model.MenuItem
import com.tcl.ai.note.home.model.createNoteListMenuItems
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet


@Composable
internal fun ShowDropDownMenu(
    isDropdownMenuExpanded: Boolean,
    menuItems: List<MenuItem>,
    onDismissRequest: () -> Unit,
    showCheckIcons: Boolean = false // 是否显示选中图标
) {

    val menuWidth = when {
        showCheckIcons && isTablet -> 207.dp
        showCheckIcons -> 186.dp
        isTablet -> 128.dp
        else -> 108.dp
    }

    // 👇 给menuWidth加动画
    val animMenuWidth by animateDpAsState(
        targetValue = menuWidth,
//        animationSpec = spring(
//            dampingRatio = Spring.DampingRatioNoBouncy,
//            stiffness = Spring.StiffnessLow
//        ),
        animationSpec = tween(
            durationMillis = 100,
            easing = FastOutLinearInEasing
        ),
        label = "MenuWidth"
    )

    val offset = IntOffset(
        x = 0,
        y = 0
    )

    Logger.d("ShowDropDownMenu","menuWidth:$menuWidth")

    if (isDropdownMenuExpanded){
        BounceScalePopup(
            onDismissRequest = onDismissRequest,
            durationMillis = 200,
            offset = offset,
            alignment = Alignment.TopEnd,
            enterTransformOrigin = BounceScaleRightTop,
            exitTransformOrigin = BounceScaleRightTop
        ) { _ ->
            Box(
                modifier = Modifier
                    .width(animMenuWidth)
                    .height(88.dp)
                    .defShadow(radius = 20.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(colorResource(R.color.drop_down_menu_bg))
                        .padding(4.dp)
                )
                {
                    menuItems.forEach { menuItem ->
                        key(menuItem.textResId) {
                            HomePopItem(
                                text = stringResource(menuItem.textResId),
                                showCheckIcons && menuItem.showCheckIcon,
                                needTalkState = showCheckIcons,
                                onClick = menuItem.action
                            )
                        }
                    }
                }
            }
        }
    }
}



/**
 * 向后兼容的组件，使用原有的参数
 */
@Composable
internal fun ShowDropDownMenuCompat(
    isDropdownMenuExpanded: Boolean,
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit,
    onShow: (Boolean) -> Unit
) {
    val menuItems = createNoteListMenuItems(
        homeNoteUiState = homeNoteUiState,
        onAction = onAction,
        onMenuShow = onShow
    )

    ShowDropDownMenu(
        isDropdownMenuExpanded = isDropdownMenuExpanded,
        menuItems = menuItems,
        onDismissRequest = { onShow(false) },
        showCheckIcons = homeNoteUiState.isClickedSort
    )
}
