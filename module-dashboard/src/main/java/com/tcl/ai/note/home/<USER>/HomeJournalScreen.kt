package com.tcl.ai.note.home.journal

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.journalhome.components.HomeAdaptiveNavigationScreen
import com.tcl.ai.note.journalhome.components.categorylist.HomeCategoryDrawerContent
import com.tcl.ai.note.home.components.base.rememberTabletLeftWeight
import com.tcl.ai.note.journalhome.vm.HomeCoordinator
import com.tcl.ai.note.journalhome.vm.rememberHomeCoordinator
import com.tcl.ai.note.journalhome.vm.state.CategoryAction
import com.tcl.ai.note.journalhome.vm.state.NoteListAction
import com.tcl.ai.note.journalhome.vm.state.HomeTitleMode
import com.tcl.ai.note.journaldashboard.ui.CreateJournalScreen
import com.tcl.ai.note.journaldashboard.ui.ROUTE_ADD_PAGE_SCREEN
import com.tcl.ai.note.journaldashboard.ui.ROUTE_JOURNAL_CONTENT_NO_ANIM_SCREEN
import com.tcl.ai.note.journaldashboard.ui.ROUTE_SETTINGS_SCREEN
import com.tcl.ai.note.journalhome.components.categorydialog.CategoryDialogScreenManager
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getCreateCoverRect
import com.tcl.ai.note.utils.setCurCoverRect
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

/**
 * 旅行日记首页
 */
@Composable
fun HomeJournalScreen(
    navController: NavController,
    pagerState: PagerState,
    showBlur: Boolean,
    onToTab: () -> Unit,
    coordinator: HomeCoordinator = rememberHomeCoordinator()
) {
    val homeUiState = coordinator.homeUiState.collectAsStateWithLifecycle()
    val categoryUiState = coordinator.categoryUiState.collectAsStateWithLifecycle()
    val rememberDrawerState = rememberDrawerState(DrawerValue.Closed)
    val coroutineScope = rememberCoroutineScope()
    var editJournal by remember { mutableStateOf<HomeNoteItemEntity?>(null) }
    // 搜索模式下禁用抽屉手势，同时支持临时禁用
    var temporaryGesturesDisabled by remember { mutableStateOf(false) }
    val gesturesEnabled by remember {
        derivedStateOf { !homeUiState.value.isSearchMode && !temporaryGesturesDisabled && !homeUiState.value.showPopupScreen } //Modified for journal
    }

    // UI Events
    val actionsHandler: (NoteListAction) -> Unit = { action ->
        Logger.d("HomeJournalScreen", "Note action: $action")
        if (action is NoteListAction.OnAddNoteClick) {
            coordinator.handleHomeAction(NoteListAction.ShowOrDismissPopScreen(true))
        } else if (action is NoteListAction.OnOpenDrawer) {
            coroutineScope.launch {
                rememberDrawerState.open()
            }
        } else if (action is NoteListAction.OnNavigateTo) {
            onToTab()
        } else if (action is NoteListAction.GoToSettings) {
            navController.navigate(ROUTE_SETTINGS_SCREEN)
        }
        coordinator.handleHomeAction(action)
    }
    val categoryActionHandler: (CategoryAction) -> Unit = { event ->
        Logger.d("HomeJournalScreen", "Category action: $event")
        if (event is CategoryAction.OnCategorySelected) {
            coroutineScope.launch {
                rememberDrawerState.close()
            }
        }
        coordinator.handleCategoryAction(event)
    }
    if (homeUiState.value.isEditMode||homeUiState.value.isSearchMode){
        BackHandler {
            // 退出编辑模式
            actionsHandler(NoteListAction.OnChangeTitleMode(HomeTitleMode.Normal))
        }
    }
    //if (pagerState.currentPage == 1) { //避免在搜索模式下，切换到 Note 页面，显示Drawer
        // If the current page is not Journal, we don't show the bottom navigation buttons
        val tabletLeftWeight = rememberTabletLeftWeight()
        HomeAdaptiveNavigationScreen(
            isTablet = false,
            leftPanelWeight = tabletLeftWeight,
            drawerState = rememberDrawerState,
            gesturesEnabled = gesturesEnabled, // 搜索模式下禁用抽屉手势
            drawerContent = {
                HomeCategoryDrawerContent(
                    categoryUiState = categoryUiState.value,
                    onCategoryAction = categoryActionHandler
                )
            },
            content = {
                HomeJournalContent(
                    navController = navController,
                    modifier = Modifier.fillMaxHeight(),
                    homeNoteUiState = homeUiState.value,
                    showBlur = showBlur,
                    onEditJournal = { journal ->
                        editJournal = journal
                        coordinator.handleHomeAction(NoteListAction.ShowOrDismissPopScreen(true))
                    },
                    onAction = actionsHandler)
                if (homeUiState.value.showPopupScreen) {
                    CreateJournalScreen(
                        navController = navController,
                        srcJournal = editJournal,
                        homeCategoryUiState = categoryUiState.value,
                        onDismissRequest = { journalId, journalTitle, coverId, addCategory ->
                            coordinator.handleHomeAction(NoteListAction.ShowOrDismissPopScreen(false))
                            coroutineScope.launch {
                                if (journalId != null && editJournal == null) {
                                    delay(10) // 延时处理，确保弹层退出
                                    val encodedTitle = URLEncoder.encode(journalTitle, StandardCharsets.UTF_8.toString())
                                    val route = if (homeUiState.value.viewType == DataStoreParam.VIEW_TYPE_GRID) {
                                        setCurCoverRect(getCreateCoverRect())
                                        "$ROUTE_ADD_PAGE_SCREEN?journalId=${journalId}&journalTitle=${encodedTitle}&coverId=${coverId}"
                                    } else {
                                        "$ROUTE_JOURNAL_CONTENT_NO_ANIM_SCREEN?journalId=${journalId}&journalTitle=${encodedTitle}&coverId=${coverId}"
                                    }
                                    navController.navigate(route)
                                } else if (journalId != null && editJournal != null) {
                                    editJournal = null
                                }
                                if (journalId != null || addCategory == true) {
                                    coordinator.observeCategoryAndNotesList()
                                }
                            }
                        },
                        onAddNewCategoryClick = {
                            categoryActionHandler.invoke(
                                CategoryAction.OnCreateNewCategoryClick(true)
                            )
                        }
                    )
                }
            })
        CategoryDialogScreenManager(screenKey = "JournalHomeScreen")
        LifecycleResumeEffect(coordinator) {
            Logger.d("HomeJournalScreen", "LifecycleResumeEffect started")
            coordinator.observeCategoryAndNotesList()
            onPauseOrDispose {
                coordinator.stopObservingCategoryAndNotesList()
            }
        }
    //}
}