package com.tcl.ai.note.home.components.pop

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.home.model.HomeCategoryItemModel
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.utils.toPx

/**
 * 删除和重命名菜单
 */
@Composable
internal fun RenameAndDeleteDropMenu(
    category: HomeCategoryItemModel,
    isDropdownMenuExpanded: Boolean,
    onAction: (HomeCategoryAction) -> Unit,
    onDismissRequest: () -> Unit
) {
    val menuWidth = 128.dp // 菜单宽度
    // 添加这些变量来计算正确的偏移量
    val layoutDirection = LocalLayoutDirection.current

    val popOffset = IntOffset(
        x = if (layoutDirection == LayoutDirection.Rtl) menuWidth.toPx.toInt() else (-menuWidth).toPx.toInt(),
        y = 2
    )

    val popupWidth: Dp = 128.dp

    if (isDropdownMenuExpanded) {
        BounceScalePopup(
            onDismissRequest = {
                onDismissRequest()
                onAction(HomeCategoryAction.OnLongClickSelected(category, false))
            },
            offset = popOffset,
        ) { closePopup ->
            Box(
                modifier = Modifier
                    .width(popupWidth)
                    .height(88.dp)
                    .defShadow(radius = 20.dp)
            ) {
                Column(
                    modifier = Modifier
                        .width(popupWidth)
                        .background(colorResource(R.color.drop_down_menu_bg))
                        .padding(4.dp)
                )
                {
                    // rename category
                    HomePopItem(
                        text = stringResource(R.string.rename_category),
                        onClick = {
                            onDismissRequest()
                            onAction(HomeCategoryAction.OnRenameCategoryClick(true, category))
                        }
                    )

                    // delete category
                    HomePopItem(
                        text = stringResource(R.string.delete_category),
                        onClick = {
                            onDismissRequest()
                            //顺序很重要 先 长按 才能 显示 删除按钮
                            onAction(HomeCategoryAction.OnShowDeleteCategoryNotesDialog(true))
                        }
                    )
                }
            }
        }
    }
}