package com.tcl.ai.note.home.model

import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.state.HomeTitleMode
// 菜单项数据类
data class MenuItem(
    val textResId: Int,
    val action: () -> Unit,
    val shouldCloseMenu: Boolean = true,
    val showCheckIcon: Boolean = false,
    val enabled: Boolean = true // 是否启用
)

/**
 * 创建笔记列表菜单项的工厂函数
 */
fun createNoteListMenuItems(
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit,
    onMenuShow: (Boolean) -> Unit
): List<MenuItem> {
    return if (homeNoteUiState.isClickedSort) {
        // 排序菜单
        listOf(
            MenuItem(
                textResId = R.string.create_date,
                action = {
                    onAction(HomeNoteListAction.OnChangeSortType(isCreateTimeSort = true))
                    onMenuShow(false)
                },
                showCheckIcon = homeNoteUiState.isCreateTimeSort
            ),
            MenuItem(
                textResId = R.string.modify_date,
                action = {
                    onAction(HomeNoteListAction.OnChangeSortType(isCreateTimeSort = false))
                    onMenuShow(false)
                },
                showCheckIcon = !homeNoteUiState.isCreateTimeSort
            )
        )
    } else {
        // 主菜单
        listOf(
            MenuItem(
                textResId = R.string.sort,
                action = {
                    onAction(HomeNoteListAction.OnClickSort(clickSorted = true))
                    onMenuShow(true)
                },
                shouldCloseMenu = false
            ),
            MenuItem(
                textResId = R.string.edit,
                action = {
                    onAction(HomeNoteListAction.OnChangeTitleMode(titleMode = HomeTitleMode.Edit))
                    onMenuShow(false)
                }
            )
        )
    }
}