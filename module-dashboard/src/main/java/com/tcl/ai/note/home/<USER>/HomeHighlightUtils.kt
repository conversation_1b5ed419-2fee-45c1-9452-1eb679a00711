package com.tcl.ai.note.home.utils

import com.tcl.ai.note.home.model.HighlightInfo
import com.tcl.ai.note.home.model.HighlightRange

// 4. 高亮工具类
object HomeHighlightUtils {

    /**
     * 查找文本中所有匹配的关键词位置
     */
    private fun findHighlightRanges(text: String, keyword: String): List<HighlightRange> {
        if (keyword.isEmpty() || text.isEmpty()) return emptyList()

        val ranges = mutableListOf<HighlightRange>()
        val lowerText = text.lowercase()
        val lowerKeyword = keyword.lowercase()

        var startIndex = 0
        while (startIndex < lowerText.length) {
            val index = lowerText.indexOf(lowerKeyword, startIndex)
            if (index == -1) break

            ranges.add(HighlightRange(index, index + keyword.length))
            startIndex = index + keyword.length
        }

        return ranges
    }

    /**
     * 为笔记项创建高亮信息（包含内容搜索）
     * @param title 标题
     * @param subtitle 副标题/摘要（用于UI显示和高亮）
     * @param content 笔记内容（用于搜索匹配）
     * @param searchKeyword 搜索关键词
     * @param isNeedHighlightContent 是否需要包含搜索内容进行高亮
     */
    fun createHighlightInfoWithContent(
        title: String,
        subtitle: String?,
        content: String?,
        searchKeyword: String,
        isNeedHighlightContent: Boolean = true
    ): HighlightInfo? {
        if (searchKeyword.isEmpty()) return null

        val titleHighlights = findHighlightRanges(title, searchKeyword)

        // 获取副标题/摘要中的高亮范围（用于UI显示）
        val contentHighlights = if (isNeedHighlightContent) {
            subtitle?.let { findHighlightRanges(it, searchKeyword) } ?: emptyList()
        } else {
            emptyList()
        }

        // 检查是否有任何匹配（包括在完整内容中的匹配）
        val hasContentMatch = content?.let {
            findHighlightRanges(it, searchKeyword).isNotEmpty()
        } == true

        val hasMatch = titleHighlights.isNotEmpty() || contentHighlights.isNotEmpty() || hasContentMatch

        // 只有找到匹配时才返回高亮信息
        return if (hasMatch) {
            HighlightInfo(
                searchKeyword = searchKeyword,
                titleHighlights = titleHighlights,
                contentHighlights = contentHighlights
            )
        } else null
    }
}