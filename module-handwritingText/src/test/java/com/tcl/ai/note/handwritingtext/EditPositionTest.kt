package com.tcl.ai.note.handwritingtext

import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试编辑位置保存和恢复功能
 */
class EditPositionTest {

    @Test
    fun testSaveAndRestoreEditPosition() = runBlocking {
        // 创建测试用的Note
        val testNote = Note(
            noteId = 1L,
            title = "Test Note",
            content = "This is a test note content for testing edit position save and restore functionality.",
            lastEditOffsetX = 0f,
            lastEditOffsetY = 0f,
            lastEditScale = 1.0f,
            lastEditCursorPosition = 0
        )

        // 模拟保存编辑位置和缩放
        val testOffsetX = 100f
        val testOffsetY = -200f
        val testScale = 1.5f
        val testCursorPosition = 25

        // 测试保存编辑位置
        val updatedNote = testNote.copy(
            lastEditOffsetX = testOffsetX,
            lastEditOffsetY = testOffsetY,
            lastEditScale = testScale,
            lastEditCursorPosition = testCursorPosition
        )

        // 验证保存的值
        assertEquals(testOffsetX, updatedNote.lastEditOffsetX, 0.01f)
        assertEquals(testOffsetY, updatedNote.lastEditOffsetY, 0.01f)
        assertEquals(testScale, updatedNote.lastEditScale, 0.01f)
        assertEquals(testCursorPosition, updatedNote.lastEditCursorPosition)

        // 测试从Note恢复MatrixInfo
        val restoredMatrix = MatrixInfo(
            offsetX = updatedNote.lastEditOffsetX,
            offsetY = updatedNote.lastEditOffsetY,
            scale = updatedNote.lastEditScale
        )

        assertEquals(testOffsetX, restoredMatrix.offsetX, 0.01f)
        assertEquals(testOffsetY, restoredMatrix.offsetY, 0.01f)
        assertEquals(testScale, restoredMatrix.scale, 0.01f)

        println("Edit position and scale save and restore test passed!")
    }

    @Test
    fun testDefaultEditPosition() {
        // 测试默认编辑位置
        val defaultNote = Note(
            noteId = 2L,
            title = "Default Note",
            content = "Default content"
        )

        // 验证默认值
        assertEquals(0f, defaultNote.lastEditOffsetX, 0.01f)
        assertEquals(0f, defaultNote.lastEditOffsetY, 0.01f)
        assertEquals(1.0f, defaultNote.lastEditScale, 0.01f)
        assertEquals(0, defaultNote.lastEditCursorPosition)

        println("Default edit position test passed!")
    }

    @Test
    fun testScaleRestoration() {
        // 测试不同缩放比例的保存和恢复
        val scaleTestCases = listOf(
            0.6f,  // 最小缩放
            1.0f,  // 默认缩放
            1.2f,  // 横屏默认缩放
            1.5f,  // 中等缩放
            2.0f,  // 较大缩放
            6.0f   // 最大缩放
        )

        scaleTestCases.forEach { testScale ->
            val scaleNote = Note(
                noteId = 100L + testScale.toLong(),
                title = "Scale Test $testScale",
                content = "Testing scale $testScale",
                lastEditOffsetX = 50f,
                lastEditOffsetY = -100f,
                lastEditScale = testScale,
                lastEditCursorPosition = 10
            )

            // 验证缩放值正确保存
            assertEquals(testScale, scaleNote.lastEditScale, 0.01f)

            // 测试MatrixInfo恢复
            val matrixInfo = MatrixInfo(
                offsetX = scaleNote.lastEditOffsetX,
                offsetY = scaleNote.lastEditOffsetY,
                scale = scaleNote.lastEditScale
            )

            assertEquals(testScale, matrixInfo.scale, 0.01f)
            assertEquals(50f, matrixInfo.offsetX, 0.01f)
            assertEquals(-100f, matrixInfo.offsetY, 0.01f)
        }

        println("Scale restoration test passed!")
    }

    @Test
    fun testEditPositionBoundaryValues() {
        // 测试边界值
        val boundaryNote = Note(
            noteId = 3L,
            title = "Boundary Test",
            content = "Testing boundary values",
            lastEditOffsetX = Float.MAX_VALUE,
            lastEditOffsetY = Float.MIN_VALUE,
            lastEditScale = 0.1f,
            lastEditCursorPosition = Int.MAX_VALUE
        )

        // 验证边界值能正确保存
        assertEquals(Float.MAX_VALUE, boundaryNote.lastEditOffsetX, 0.01f)
        assertEquals(Float.MIN_VALUE, boundaryNote.lastEditOffsetY, 0.01f)
        assertEquals(0.1f, boundaryNote.lastEditScale, 0.01f)
        assertEquals(Int.MAX_VALUE, boundaryNote.lastEditCursorPosition)

        println("Boundary values test passed!")
    }
}
