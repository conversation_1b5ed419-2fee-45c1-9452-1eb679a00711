package com.tcl.ai.note.handwritingtext

import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试页面查看位置保存和恢复功能（简化版）
 */
class EditPositionTest {

    @Test
    fun testSaveAndRestoreViewPosition() = runBlocking {
        // 创建测试用的Note
        val testNote = Note(
            noteId = 1L,
            title = "Test Note",
            content = "This is a test note content for testing view position save and restore functionality.",
            lastViewOffsetX = 0f,
            lastViewOffsetY = 0f,
            lastViewScale = 1.0f
        )

        // 模拟保存页面查看位置和缩放
        val testOffsetX = 150f
        val testOffsetY = -200f
        val testScale = 1.5f

        // 测试保存查看位置
        val updatedNote = testNote.copy(
            lastViewOffsetX = testOffsetX,
            lastViewOffsetY = testOffsetY,
            lastViewScale = testScale
        )

        // 验证保存的值
        assertEquals(testOffsetX, updatedNote.lastViewOffsetX, 0.01f)
        assertEquals(testOffsetY, updatedNote.lastViewOffsetY, 0.01f)
        assertEquals(testScale, updatedNote.lastViewScale, 0.01f)

        // 测试从Note恢复MatrixInfo
        val restoredMatrix = MatrixInfo(
            offsetX = updatedNote.lastViewOffsetX,
            offsetY = updatedNote.lastViewOffsetY,
            scale = updatedNote.lastViewScale
        )

        assertEquals(testOffsetX, restoredMatrix.offsetX, 0.01f)
        assertEquals(testOffsetY, restoredMatrix.offsetY, 0.01f)
        assertEquals(testScale, restoredMatrix.scale, 0.01f)

        println("View position and scale save and restore test passed!")
    }

    @Test
    fun testDefaultViewPosition() {
        // 测试默认查看位置
        val defaultNote = Note(
            noteId = 2L,
            title = "Default Note",
            content = "Default content"
        )

        // 验证默认值
        assertEquals(0f, defaultNote.lastViewOffsetX, 0.01f)
        assertEquals(0f, defaultNote.lastViewOffsetY, 0.01f)
        assertEquals(1.0f, defaultNote.lastViewScale, 0.01f)

        println("Default view position test passed!")
    }

    @Test
    fun testScaleRestoration() {
        // 测试不同缩放比例的保存和恢复
        val scaleTestCases = listOf(
            0.6f,  // 最小缩放
            1.0f,  // 默认缩放
            1.2f,  // 横屏默认缩放
            1.5f,  // 中等缩放
            2.0f,  // 较大缩放
            6.0f   // 最大缩放
        )

        scaleTestCases.forEach { testScale ->
            val scaleNote = Note(
                noteId = 100L + testScale.toLong(),
                title = "Scale Test $testScale",
                content = "Testing scale $testScale",
                lastViewOffsetX = 50f,
                lastViewOffsetY = -100f,
                lastViewScale = testScale
            )

            // 验证缩放值正确保存
            assertEquals(testScale, scaleNote.lastViewScale, 0.01f)

            // 测试MatrixInfo恢复
            val matrixInfo = MatrixInfo(
                offsetX = scaleNote.lastViewOffsetX,
                offsetY = scaleNote.lastViewOffsetY,
                scale = scaleNote.lastViewScale
            )

            assertEquals(testScale, matrixInfo.scale, 0.01f)
            assertEquals(50f, matrixInfo.offsetX, 0.01f)
            assertEquals(-100f, matrixInfo.offsetY, 0.01f)
        }

        println("Scale restoration test passed!")
    }

    @Test
    fun testViewPositionBoundaryValues() {
        // 测试边界值
        val boundaryNote = Note(
            noteId = 3L,
            title = "Boundary Test",
            content = "Testing boundary values",
            lastViewOffsetX = Float.MAX_VALUE,
            lastViewOffsetY = Float.MIN_VALUE,
            lastViewScale = 0.1f
        )

        // 验证边界值能正确保存
        assertEquals(Float.MAX_VALUE, boundaryNote.lastViewOffsetX, 0.01f)
        assertEquals(Float.MIN_VALUE, boundaryNote.lastViewOffsetY, 0.01f)
        assertEquals(0.1f, boundaryNote.lastViewScale, 0.01f)

        println("Boundary values test passed!")
    }
}
