package com.tcl.ai.note.handwritingtext.ui.utils.scale

import android.content.pm.Capability
import android.graphics.Rect
import android.graphics.RectF
import android.os.Handler
import android.os.Looper
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener
import androidx.annotation.FloatRange
import com.tcl.ai.note.GlobalContext.Companion.appContext
import com.tcl.ai.note.handwritingtext.ui.utils.scale.OffsetAndScaleEdgeLimitAction.CenterHorizontalEdgeLimitAction
import com.tcl.ai.note.utils.Logger
import java.util.concurrent.atomic.AtomicInteger

/**
 *
 */
data class MatrixInfo(
    var offsetX: Float = 0f,
    var offsetY: Float = 0f,
    var scale: Float = 1.0f,
) {
    // 缩放点已经经过OffsetAndScaleHandler处理，始终以(0, 0)为缩点中心
    // 所以这里缩点中心直接写死为(0, 0)
    val scaleCenterX = 0f
    val scaleCenterY = 0f
}

class OffsetAndScaleHandler(
    val defaultMatrixInfo: MatrixInfo = MatrixInfo(),
    /**
     * @param overRect 相对于父View的位置。超出父View为负数，否则为正数
     * @param isGesture 是否是手势处理的缩放位移逻辑
     */
    var onScrollAndScaleStart: (MatrixInfo, overRect: RectF, isGesture: Boolean) -> Unit = { _, _, _ -> },
    var onScrollingAndScaling: (MatrixInfo, overRect: RectF, isGesture: Boolean) -> Unit = { _, _, _ -> },
    var onScrollAndScaleEnd: (MatrixInfo, overRect: RectF, isGesture: Boolean) -> Unit = { _, _, _ -> },
) {
    private val mainHandler = Handler(Looper.getMainLooper())
    // 因为创建频率太高了，这里写死。复用同一个对象。
    // 本轮缩放位移信息，缩放位移信息结束后重置
    private val innerMatrixInfo: InnerMatrixInfo = defaultMatrixInfo.toInnerMatrixInfo()

    // 回调的入参，复用同一个对象。避免高频创建。
    // 注意不要使用stateflow传递outMatrixInfo值。
    private val recycleOnStartMatrixInfo = RecycleReuseMatrixInfo()
    private val recycleOnScrollingAndScalingMatrixInfo = RecycleReuseMatrixInfo(8)
    private val recycleOnEndMatrixInfo = RecycleReuseMatrixInfo()

    // 缩放大小限制
    private var SCALE_MIN = 0.8F
    private var SCALE_MAX = 3F

    // 限制位移, 通过高阶函数可以提高外部扩展
    // 把当前缩放值传递到扩展函数中，外部某些变量，可能不需要乘以缩放因子
    private var OFFSET_Y_MIN_FUNC: (scale: Float) -> Float = { -Float.MAX_VALUE }
    private var OFFSET_Y_MAX_FUNC: (scale: Float) -> Float = { Float.MAX_VALUE }
    private var OFFSET_X_MIN_FUNC: (scale: Float) -> Float = { -Float.MAX_VALUE }
    private var OFFSET_X_MAX_FUNC: (scale: Float) -> Float = { Float.MAX_VALUE }

    // 正在缩放
    val isScaling get() = scaleGestureDetector.isInProgress
    // 正在位移
    var isScrolling = false
        private set
    // 本轮手势是否触发了位移或者缩放
    private var isAlreadyScrollOrScale = false

    // 是否允许缩放，关闭后，仅支持位移。（仅位移没有单独测试过）
    var enableScale = true
        private set

    // 缩放位移信息
    private val recycleOutMatrixInfo = RecycleReuseMatrixInfo(3)
    val matrixInfo get() = recycleOutMatrixInfo.obtain(innerMatrixInfo)

    // 缩放比例
    val scale get() = innerMatrixInfo.scale
    val offsetX get() = innerMatrixInfo.offsetX
    val offsetY get() = innerMatrixInfo.offsetY

    // view的可视范围
    private val visibleRect = Rect()

    /**
     * 处理缩放手势
     */
    private val scaleGestureDetector = object : SimpleOnScaleGestureListener() {
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            Logger.d(TAG, "scaleGestureDetector, onScaleBegin")
            dealScaleDetector(detector)
            return true
        }

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            Logger.d(TAG, "scaleGestureDetector, onScale")
            dealScaleDetector(detector)
            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            Logger.d(TAG, "scaleGestureDetector, onScaleEnd")
            dealScaleDetector(detector)
        }
    }.let { scaleGestureListener ->
        // 创建缩放手势检测
        ScaleGestureDetector(appContext, scaleGestureListener, mainHandler)
    }

    private val offsetGestureDetector = object : GestureDetector.SimpleOnGestureListener() {
        override fun onDown(e: MotionEvent): Boolean {
            isScrolling = false
            return true
        }

        override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
            isScrolling = true
            Logger.d(TAG, "onScroll, distanceX: $distanceX, distanceY: $distanceY")
            // 偏移值计算
            dealOffsetDetector(distanceX, distanceY)
            return false
        }

        fun onUp(e: MotionEvent) {
            isScrolling = false
            Logger.d(TAG, "onUp: $e")
        }

        override fun onFling(firstEvent: MotionEvent?, curEvent: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
            Logger.d(TAG, "onFling, velocityX: $velocityX, velocityY: $velocityY")
            val action = curEvent.actionMasked
            if (action == MotionEvent.ACTION_CANCEL
                || curEvent.flags == MotionEvent.FLAG_CANCELED
            ) {
                return false
            }
            // TODO 没有实现fling
            return false
        }
    }.let { moveGestureListener ->
        // 创建位移手势检测
        object : GestureDetector(appContext, moveGestureListener, mainHandler) {
            override fun onTouchEvent(ev: MotionEvent): Boolean {
                val isInterrupted = super.onTouchEvent(ev)
                if (ev.actionMasked == MotionEvent.ACTION_UP) {
                    moveGestureListener.onUp(ev)
                }
                return isInterrupted
            }
        }
    }

    // 处理位移手势
    private fun dealOffsetDetector(dx: Float, dy: Float) {
        Logger.d(TAG, "dealOffsetDetector: dx: $dx, dy: $dy")
        // 偏移值计算
        doOffsetAndScale(dx, dy)
    }

    // 处理缩放手势
    private fun dealScaleDetector(detector: ScaleGestureDetector) {
        // 偏移值计算, 包含(focusX,focusY)换成(0,0)的计算
        val offsetX = detector.focusX - (detector.focusX - innerMatrixInfo.offsetX) * detector.getScaleFactor()
        val offsetY = detector.focusY - (detector.focusY - innerMatrixInfo.offsetY) * detector.getScaleFactor()
        val dx = innerMatrixInfo.offsetX - offsetX
        val dy = innerMatrixInfo.offsetY - offsetY
        Logger.d(
            TAG,
            "dealScaleDetector: offsetX: $offsetX, offsetY: $offsetY, scale: ${detector.getScaleFactor()}, focusX: ${detector.focusX}, focusY: ${detector.focusY}"
        )
        doOffsetAndScale(
            dx,
            dy,
            detector.getScaleFactor(),
        )
    }

    /**
     * 缩放位移.
     *
     * 把缩放的中心点换成（0,0）适配安格斯画布
     *
     * @param deltaX 增量X
     * @param deltaY 增量Y
     * @param deltaScale 缩放增量
     * @param focusX 缩放中心X
     * @param focusY 缩放中心Y
     */
    private fun doOffsetAndScale(
        deltaX: Float = offsetX,
        deltaY: Float = offsetY,
        deltaScale: Float = 1f,
    ) {
        Logger.d(
            TAG,
            "doOffsetAndScale start: deltaX: $deltaX, deltaY: $deltaY, deltaScale: $deltaScale, innerMatrixInfo: $innerMatrixInfo"
        )
        val scale = innerMatrixInfo.scale * deltaScale
        if (scale !in SCALE_MIN..SCALE_MAX) {
            return
        }
        // 缩放设置
        innerMatrixInfo.scale = scale
        // 位移设置
        innerMatrixInfo.offsetX -= deltaX
        innerMatrixInfo.offsetY -= deltaY
        // 修正偏移量
        executeEdgeLimitAction(innerMatrixInfo)
        // 获取view的四条边离visibleRect距离
//        getOverRect(innerMatrixInfo)
        Logger.d(TAG, "doOffsetAndScale end, innerMatrixInfo: $innerMatrixInfo")
        // 回调缩放位移
        if (isAlreadyScrollOrScale == false) {
            isAlreadyScrollOrScale = true
            onScrollAndScaleStart(recycleOnStartMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), true)
        }
        onScrollingAndScaling(recycleOnScrollingAndScalingMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), true)
    }

    /**
     *  @return 获取视图上下左右的边缘超出visibleRect的距离，超出visibleRect为负数，在visibleRect内为正数
     */
    private fun getOverRect(innerMatrixInfo: InnerMatrixInfo): RectF {
        // 缩放值修正
        innerMatrixInfo.scale = innerMatrixInfo.scale.coerceIn(
            SCALE_MIN,
            SCALE_MAX,
        )
        ////////////////////////////////////////////////////////////////////////////////////
        // 水平修正，因为放大之后，会超出屏幕范围。需要把超出的范围添加进来
        // 左滑限制
        val minCorrectScaleOffsetX = visibleRect.width() * (1 - scale)
        val minimumX = OFFSET_X_MIN_FUNC(scale) * scale + minCorrectScaleOffsetX
        // 右滑限制
        val maxCorrectScaleOffsetX = visibleRect.left * scale
        val maximumX = OFFSET_X_MAX_FUNC(scale) * scale + maxCorrectScaleOffsetX
        ////////////////////////////////////////////////////////////////////////////////////
        // 垂直修正，因为放大之后，会超出屏幕范围。需要把超出的范围添加进来
        // 上滑限制
        val minCorrectScaleOffsetY = visibleRect.height() * (1 - scale)
        val minimumY = OFFSET_Y_MIN_FUNC(scale) * scale + minCorrectScaleOffsetY
        // 下滑限制
        val maxCorrectScaleOffsetY = visibleRect.top * scale
        val maximumY = OFFSET_Y_MAX_FUNC(scale) * scale + maxCorrectScaleOffsetY
        ////////////////////////////////////////////////////////////////////////////////////
        val leftPadding = visibleRect.left
        val rightPadding = visibleRect.right - visibleRect.width()
        val topPadding = visibleRect.top
        val bottomPadding = visibleRect.bottom - visibleRect.height()
        val overRectF = RectF(
            innerMatrixInfo.offsetX,
            innerMatrixInfo.offsetY,
            minimumX - innerMatrixInfo.offsetX + leftPadding + rightPadding,
            minimumY - innerMatrixInfo.offsetY + topPadding + bottomPadding,
        )
        Logger.d(TAG, "getOverRectF, overRectF: $overRectF, \nvisibleRect: $visibleRect")
        return overRectF
    }

    /**
     * 边缘滑动限制
     *
     * @param customEdgeLimitAction 自定义边缘限制行为
     */
    var customEdgeLimitAction: ((MatrixInfo, overRect: RectF) -> MatrixInfo) = CenterHorizontalEdgeLimitAction
    private val recycleEdgeLimitMatrixInfo = RecycleReuseMatrixInfo(3)
    private fun executeEdgeLimitAction(innerMatrixInfo: InnerMatrixInfo) {
        val overRect = getOverRect(innerMatrixInfo)
        val matrixInfo = customEdgeLimitAction(
            recycleEdgeLimitMatrixInfo.obtain(innerMatrixInfo),
            overRect,
        )
        innerMatrixInfo.offsetX = matrixInfo.offsetX
        innerMatrixInfo.offsetY = matrixInfo.offsetY
        innerMatrixInfo.scale = matrixInfo.scale
        Logger.d(TAG, "executeCustomEdgeLimitAction, overRect：$overRect, innerMatrixInfo: $innerMatrixInfo")
    }

    /**
     * 设置可视边界
     *
     * 注：会根据可视边界修正MatrixInfo
     */
    fun setVisibleSizeAndGetMatrixInfo(left: Int, top: Int, right: Int, bottom: Int): MatrixInfo {
        visibleRect.set(left, top, right, bottom)
        // 修正边界值
        executeEdgeLimitAction(innerMatrixInfo)
        Logger.d(TAG, "setVisibleSize, visibleRect: $visibleRect")
        return matrixInfo
    }

    fun setScaleLimit(
        @FloatRange(0.0, Double.MAX_VALUE) minScale: Float,
        @FloatRange(0.0, Double.MAX_VALUE) maxScale: Float
    ) {
        SCALE_MIN = minScale
        SCALE_MAX = maxScale
    }

    /**
     * maxOffsetY下滑y是正数，maxOffsetX右滑x是正数
     * minOffsetY上滑y是负数，minOffsetX左滑x是负数
     *
     * 默认带缩放，如果某些偏移值不需要带缩放，需要自己处理
     */
    fun setOffsetLimit(
        minOffsetX: (scale: Float) -> Float = OFFSET_X_MIN_FUNC,
        maxOffsetX: (scale: Float) -> Float = OFFSET_X_MAX_FUNC,
        minOffsetY: (scale: Float) -> Float = OFFSET_Y_MIN_FUNC,
        maxOffsetY: (scale: Float) -> Float = OFFSET_Y_MAX_FUNC,
    ) {
        OFFSET_X_MIN_FUNC = minOffsetX
        OFFSET_X_MAX_FUNC = maxOffsetX
        OFFSET_Y_MIN_FUNC = minOffsetY
        OFFSET_Y_MAX_FUNC = maxOffsetY
        Logger.d(
            TAG,
            "setOffsetLimit, minOffsetX: $minOffsetX, minOffsetY: $minOffsetY, maxOffsetX: $maxOffsetX, maxOffsetY: $maxOffsetY"
        )
    }

    /**
     * 绝对缩放，设置多少就是多少
     * @param newMatrixInfo 目标缩放位移
     */
    fun updateScaleInfo(newMatrixInfo: MatrixInfo) {
        Logger.d(TAG, "updateScaleInfo start, newMatrixInfo: $newMatrixInfo, innerMatrixInfo: $innerMatrixInfo")
        innerMatrixInfo.scale = newMatrixInfo.scale
        innerMatrixInfo.offsetX = newMatrixInfo.offsetX
        innerMatrixInfo.offsetY = newMatrixInfo.offsetY
        executeEdgeLimitAction(innerMatrixInfo)
        onScrollAndScaleStart(recycleOnStartMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), false)
        onScrollingAndScaling(recycleOnScrollingAndScalingMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), false)
        onScrollAndScaleEnd(recycleOnEndMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), false)
        Logger.d(TAG, "updateScaleInfo end, innerMatrixInfo: $innerMatrixInfo")
    }

    /**
     * [doScale] 和 [doScaleEnd] 组合成一对，实现了[updateScaleInfo]函数，应对某些业务需求
     *
     * [isDoScaleStart] 判断是否是首次调用[doScale]。 如果是首次，需要回调 [onScrollAndScaleStart] 函数
     *
     * @param newMatrixInfo 目标缩放位移，绝对值
     */
    private var isDoScaleStart = false
    fun doScale(newMatrixInfo: MatrixInfo) {
        innerMatrixInfo.scale = newMatrixInfo.scale
        innerMatrixInfo.offsetX = newMatrixInfo.offsetX
        innerMatrixInfo.offsetY = newMatrixInfo.offsetY
        executeEdgeLimitAction(innerMatrixInfo)
        if (isDoScaleStart == false) {
            isDoScaleStart = true
            onScrollAndScaleStart(recycleOnStartMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), false)
        }
        onScrollingAndScaling(recycleOnScrollingAndScalingMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), false)
        Logger.i(TAG, "doScale, innerMatrixInfo: $innerMatrixInfo")
    }

    /**
     * [doScale] 和 [doScaleEnd] 组合成一对，实现了[updateScaleInfo]函数，应对某些业务需求
     * @param newMatrixInfo 目标缩放位移
     */
    fun doScaleEnd(newMatrixInfo: MatrixInfo) {
        innerMatrixInfo.scale = newMatrixInfo.scale
        innerMatrixInfo.offsetX = newMatrixInfo.offsetX
        innerMatrixInfo.offsetY = newMatrixInfo.offsetY
        executeEdgeLimitAction(innerMatrixInfo)
        onScrollAndScaleEnd(recycleOnEndMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), false)
        // 结束doScale，isDoScaleStart设置为false
        isDoScaleStart = false
        Logger.i(TAG, "doScaleEnd, innerMatrixInfo: $innerMatrixInfo")
    }

    /**
     * 重置缩放位移
     *
     * [defaultMatrixInfo] 重置回默认缩放位置值
     */
    fun resetToDefaultMatrix() {
        updateScaleInfo(defaultMatrixInfo)
    }

    /**
     * 实时检测手势
     */
    fun detectGesture(ev: MotionEvent) {
        offsetGestureDetector.onTouchEvent(ev)
        if (ev.pointerCount >= 2) {
            scaleGestureDetector.onTouchEvent(ev)
        }
        if (isAlreadyScrollOrScale == true
            && (ev.actionMasked == MotionEvent.ACTION_UP || ev.actionMasked == MotionEvent.ACTION_CANCEL)
        ) {
            isAlreadyScrollOrScale = false
            onScrollAndScaleEnd(recycleOnEndMatrixInfo.obtain(innerMatrixInfo), getOverRect(innerMatrixInfo), true)
        }
    }

    companion object {
        private const val TAG: String = "OffsetAndScaleHandler"

        private data class InnerMatrixInfo(
            var offsetX: Float = 0f,
            var offsetY: Float = 0f,
            var scale: Float = 1.0f,
        )

        /**
         * 循环使用MatrixInfo, 避免创建对象频率太高
         * 注: 越高频的函数, 可能会引起StateFlow跳过, 可以调大[capability]的值
         */
        private class RecycleReuseMatrixInfo(capability: Int = 2) {
            private var index = 0
            private var reuseObjList = Array(capability) { _ -> MatrixInfo() }

            fun obtain(): MatrixInfo {
                val reuseObj = reuseObjList[index++]
                if (index == reuseObjList.size) {
                    index = 0
                }
                return reuseObj
            }

            fun obtain(innerMatrixInfo: InnerMatrixInfo): MatrixInfo {
                val matrixInfo = obtain()
                matrixInfo.scale = innerMatrixInfo.scale
                matrixInfo.offsetX = innerMatrixInfo.offsetX
                matrixInfo.offsetY = innerMatrixInfo.offsetY
                return matrixInfo
            }
        }

        private fun InnerMatrixInfo.toMatrixInfo(): MatrixInfo {
            return MatrixInfo(offsetX, offsetY, scale)
        }

        private fun MatrixInfo.toInnerMatrixInfo(): InnerMatrixInfo {
            return InnerMatrixInfo(offsetX, offsetY, scale)
        }
    }
}

object OffsetAndScaleEdgeLimitAction {
    // 无限制
    val OffsetAndScaleHandler.NoEdgeLimitAction
        get() = { matrixInfo: MatrixInfo, overRect: RectF -> matrixInfo }

    // 水平和垂直都居中
    val OffsetAndScaleHandler.CenterEdgeLimitAction
        get() = CenterEdgeLimitAction@{ matrixInfo: MatrixInfo, overRect: RectF ->
            ////////////////////////////////////////////////////////////////////////////////////
            if (overRect.left > 0) {
                // 限制右滑
                matrixInfo.offsetX -= overRect.left
            }
            if (overRect.right > 0) {
                // 限制左滑
                matrixInfo.offsetX += overRect.right
            }
            if (overRect.left + overRect.right >= 0) {
                // 宽度小于父view，水平居中
                matrixInfo.offsetX = (overRect.left + overRect.right) / 2
            }
            ////////////////////////////////////////////////////////////////////////////////////
            if (overRect.top > 0) {
                // 限制右滑
                matrixInfo.offsetY -= overRect.top
            }
            if (overRect.bottom > 0) {
                // 限制左滑
                matrixInfo.offsetY += overRect.bottom
            }
            if (overRect.top + overRect.bottom >= 0) {
                // 宽度小于父view，垂直居中
                matrixInfo.offsetY = (overRect.top + overRect.bottom) / 2
            }
            return@CenterEdgeLimitAction matrixInfo
        }

    // 水平居中
    val OffsetAndScaleHandler.CenterHorizontalEdgeLimitAction
        get() = CenterHorizontalEdgeLimitAction@{ matrixInfo: MatrixInfo, overRect: RectF ->
            ////////////////////////////////////////////////////////////////////////////////////
            if (overRect.left > 0) {
                // 限制右滑
                matrixInfo.offsetX -= overRect.left
            }
            if (overRect.right > 0) {
                // 限制左滑
                matrixInfo.offsetX += overRect.right
            }
            if (overRect.left + overRect.right >= 0) {
                // 宽度小于父view，水平居中
                matrixInfo.offsetX = (overRect.left + overRect.right) / 2
            }
            ////////////////////////////////////////////////////////////////////////////////////
            if (overRect.top >= 0) {
                // 限制下滑
                // 0f是为了保持View置顶，不允许下滑
                matrixInfo.offsetY = 0f
            }
            if (overRect.bottom > 0) {
                // 限制上滑
                matrixInfo.offsetY += overRect.bottom
            }
            return@CenterHorizontalEdgeLimitAction matrixInfo
        }
}