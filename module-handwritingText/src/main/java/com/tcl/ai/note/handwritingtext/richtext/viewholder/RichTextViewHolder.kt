package com.tcl.ai.note.handwritingtext.richtext.viewholder

import android.content.Context
import android.content.ContextWrapper
import android.util.TypedValue
import android.view.View
import android.view.View.OnAttachStateChangeListener
import android.view.View.OnClickListener
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.richtext.converter.toRichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Alignment
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_BackgroundColor
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Bold
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontColor
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontSize
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_IndentLeft
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_IndentRight
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Italic
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListBullet
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListNumber
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Strikethrough
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Underline
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.handwritingtext.vm.event.RichTextOperateEvent
import com.tcl.ai.note.handwritingtext.vm.event.RichTextStyleActionEvent
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 富文本编辑器ViewHolder
 *
 * 负责管理富文本编辑器(AREditText)的样式控制、事件处理和生命周期管理。
 *
 * ## 主要职责：
 * 1. **样式管理**: 初始化和管理各种富文本样式对象(粗体、斜体、列表等)
 * 2. **事件处理**: 监听并处理来自RichTextEventManager的样式操作事件
 * 3. **状态同步**: 通过StyleStatusDelegate将样式状态同步到工具栏UI
 * 4. **生命周期管理**: 管理协程、监听器和资源的创建与释放
 * 5. **内容回调**: 当富文本内容或样式发生变化时，通知外部进行数据持久化
 *
 * ## 架构设计：
 * - 使用委托模式(StyleStatusDelegate)处理样式状态回调，避免代码重复
 * - 通过RichTextEventManager进行事件驱动的样式操作
 * - 支持协程生命周期管理，确保在组件销毁时正确清理资源
 *
 * @param context 上下文对象，用于获取LifecycleOwner和资源访问
 * @param areEditText 富文本编辑器实例，承载实际的文本编辑和样式渲染
 * @param onRichTextChanged 内容变化回调，用于通知外部进行数据保存
 */
class RichTextViewHolder(
    private val context: Context,
    private val areEditText: AREditText,
    private val onRichTextChanged: ((content: String, style: RichTextStyleEntity) -> Unit)? = null
) : OnClickListener , StyleStatusListener by StyleStatusDelegate(){

    // ==================== 样式对象管理 ====================
    /** 粗体样式控制器 */
    private var areBold: ARE_Bold? = null
    /** 斜体样式控制器 */
    private var areItalic: ARE_Italic? = null
    /** 下划线样式控制器 */
    private var areUnderline: ARE_Underline? = null
    /** 删除线样式控制器(暂时禁用) */
    private var areStrikethrough: ARE_Strikethrough? = null
    /** 文本对齐样式控制器(暂时禁用) */
    private var areAlignment: ARE_Alignment? = null
    /** 右缩进样式控制器(暂时禁用) */
    private var areIndentRight: ARE_IndentRight? = null
    /** 左缩进样式控制器(暂时禁用) */
    private var areIndentLeft: ARE_IndentLeft? = null
    /** 有序列表样式控制器 */
    private var areListNumber: ARE_ListNumber? = null
    /** 无序列表样式控制器 */
    private var areListBullet: ARE_ListBullet? = null
    /** 字体大小样式控制器(暂时禁用) */
    private var areFontSize: ARE_FontSize? = null
    /** 字体颜色样式控制器(暂时禁用) */
    private var areFontColor: ARE_FontColor? = null
    /** 背景颜色样式控制器(暂时禁用) */
    private var areBackgroundColor: ARE_BackgroundColor? = null
    /** 待办事项样式控制器 */
    private var areTodo: ARE_Upcoming? = null

    // ==================== 生命周期管理 ====================
    private val TAG = "RichTextViewHolder"
    /**
     * 生命周期拥有者，用于协程管理
     * 支持从Context或ContextWrapper中提取LifecycleOwner
     */
    private val lifecycleOwner: LifecycleOwner? = when (context) {
        is LifecycleOwner -> context
        is ContextWrapper -> (context as? ContextWrapper)?.baseContext as? LifecycleOwner
        else -> null
    }
    /** 协程作用域，用于事件监听的协程管理 */
    private val lifecycleScope: CoroutineScope? = lifecycleOwner?.lifecycleScope

    /** 标记ViewHolder是否已释放，防止重复释放和在释放后继续操作 */
    private var isReleased = false
    /** 事件监听协程Job，用于取消事件监听 */
    private var eventJob: Job? = null

    init {
        initEditView()
        initStyles()
        observeEvents()
        initListeners()
    }

    /**
     * 初始化编辑器视图配置
     *
     * 设置编辑器的基本属性，包括：
     * - 根据设备类型(平板/手机)设置不同的内边距
     * - 配置文本颜色、字体大小、行高等基础样式
     * - 启用焦点和触摸模式
     * - 控制监听器的启动时机，避免产生无效的撤销/重做记录
     */
    private fun initEditView() {
        // 根据设备类型设置水平内边距
        val paddingHorizontal = DisplayUtils.dp2px(
            isTablet.judge(PADDING_HORIZONTAL_TABLET_DP, PADDING_HORIZONTAL_PHONE_DP)
        )
        // 根据设备类型设置垂直内边距
        val paddingVertical = DisplayUtils.dp2px(
            isTablet.judge(PADDING_VERTICAL_TABLET_DP, PADDING_VERTICAL_PHONE_DP)
        )
        // 设置编辑器内边距，底部为0以便与其他UI元素紧密衔接
        areEditText.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, 0)

        // 启用焦点和触摸模式，确保用户可以正常交互
        areEditText.isFocusable = true
        areEditText.setFocusableInTouchMode(true)

        // 设置文本颜色为主题色
        areEditText.setTextColor(ContextCompat.getColor(context, R.color.text_title))

        // 暂停所有监听器，避免初始化过程中产生无效的撤销/重做记录
        areEditText.stopAllMonitor()

        // 设置基础文本样式
        areEditText.textSize = TEXT_SIZE_SP.toFloat()
        areEditText.setLineHeight(TypedValue.COMPLEX_UNIT_SP, LINE_HEIGHT_SP.toFloat())

        // 初始化完成后启动监听器，开始正常的编辑监控
        areEditText.startAllMonitor()
    }

    /**
     * 初始化各种监听器
     *
     * 设置编辑器的事件监听，包括：
     * - 点击事件监听
     * - 样式状态变化监听(通过委托模式处理)
     * - 视图附加/分离状态监听(管理待办事项消息和工具栏状态)
     */
    private fun initListeners() {
        // 设置点击监听器
        areEditText.setOnClickListener(this)

        // 设置样式状态监听器，this指向StyleStatusDelegate委托实现
        // 当光标位置改变或选择文本时，会回调相应的样式状态方法
        areEditText.setStyleStatusListener(this)

        // 监听视图的附加/分离状态，管理相关资源
        areEditText.addOnAttachStateChangeListener(object : OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                // 视图附加到窗口时，注册待办事项消息监听
                registerTodoMessage()
            }

            override fun onViewDetachedFromWindow(v: View) {
                // 视图从窗口分离时，清理资源并恢复工具栏状态
                unregisterTodoMessage()
                RichTextEventManager.restoreToolBarStyleState()
            }
        })
    }

    /**
     * 初始化富文本样式控制器
     *
     * 创建并注册各种样式对象到编辑器中。每个样式对象负责处理特定的文本格式：
     * - 字符级样式：粗体、斜体、下划线等
     * - 段落级样式：列表、待办事项等
     *
     * 注意：部分样式功能暂时禁用，以避免对待办功能产生影响
     */
    private fun initStyles() {
        // ==================== 字符级样式 ====================

        /** 粗体样式：支持选中文本或光标位置的粗体格式 */
        areBold = ARE_Bold().apply {
            areEditText.setInStylesList(this)
        }

        /** 斜体样式：支持选中文本或光标位置的斜体格式 */
        areItalic = ARE_Italic().apply {
            areEditText.setInStylesList(this)
        }

        /** 下划线样式：支持选中文本或光标位置的下划线格式 */
        areUnderline = ARE_Underline().apply {
            areEditText.setInStylesList(this)
        }

        // ==================== 段落级样式 ====================

        /** 有序列表样式：支持数字编号的列表格式 */
        areListNumber = ARE_ListNumber().apply {
            areEditText.setInStylesList(this)
        }

        /** 无序列表样式：支持项目符号的列表格式 */
        areListBullet = ARE_ListBullet().apply {
            areEditText.setInStylesList(this)
        }

        /**
         * 待办事项样式：支持可勾选的任务列表
         * 设置状态变化监听器，当用户勾选/取消勾选时触发内容保存
         */
        areTodo = ARE_Upcoming(context).apply {
            areEditText.setInStylesList(this)
            this.setOnCheckStateListener {
                notifyRichTextChange()
            }
        }

        // ==================== 暂时禁用的样式功能 ====================
        // TODO: 以下功能暂时禁用，避免对待办功能产生影响
        // 后续版本可根据需求逐步启用

//        areStrikethrough = ARE_Strikethrough().apply {
//            areEditText.setInStylesList(this)
//        }
//        areIndentRight = ARE_IndentRight(areEditText).apply {
//            areEditText.setInStylesList(this)
//        }
//        areIndentLeft = ARE_IndentLeft(areEditText).apply {
//            areEditText.setInStylesList(this)
//        }
//
//        areFontSize = ARE_FontSize().apply {
//            areEditText.setInStylesList(this)
//        }
//        areFontColor = ARE_FontColor().apply {
//            areEditText.setInStylesList(this)
//        }
//        areBackgroundColor = ARE_BackgroundColor().apply {
//            areEditText.setInStylesList(this)
//        }
//
//        areAlignment = ARE_Alignment().apply {
//            areEditText.setInStylesList(this)
//        }
    }

    /**
     * 观察富文本样式操作事件
     *
     * 通过协程监听RichTextEventManager发出的样式操作事件，
     * 确保只在编辑器附加到窗口且未释放的状态下处理事件。
     *
     * 事件流程：
     * 1. 用户点击工具栏按钮 -> RichTextEventManager发出事件
     * 2. 此方法接收事件 -> handleStyleEvent处理具体操作
     * 3. 样式应用到编辑器 -> 触发内容保存回调
     */
    private fun observeEvents() {
        // 取消之前的事件监听协程，避免重复监听
        eventJob?.cancel()

        // 在生命周期作用域内启动新的事件监听协程
        eventJob = lifecycleScope?.launch {
            RichTextEventManager.richTextStyleActionEvent.collect { event ->
                event?.let {
                    // 只在编辑器正常状态下处理事件
                    if (areEditText.isAttachedToWindow && !isReleased) {
                        handleStyleEvent(it)
                    }
                    // 注意：事件处理完成后不清除，允许多个ViewHolder同时监听
                    // RichTextEventManager.clearStyleEvent() // 处理完事件后清除
                }
            }
        }
    }

    /**
     * 处理富文本操作事件
     *
     * 处理来自RichTextOperateEventManager的操作事件，主要包括：
     * - 撤销操作：恢复到上一个编辑状态
     * - 重做操作：重新应用已撤销的编辑操作
     *
     * 注意：此方法目前未被调用，保留用于未来扩展
     *
     * @param operateEvent 操作事件对象
     */
    private fun handleOperateEvent(operateEvent: RichTextOperateEvent) {
        when (operateEvent) {
            is RichTextOperateEvent.Undo -> {
                // 执行撤销操作，恢复编辑器到上一个状态
                areEditText.undo()
            }

            is RichTextOperateEvent.Redo -> {
                // 执行重做操作，重新应用已撤销的更改
                areEditText.redo()
            }

            else -> {
                // 其他操作事件暂不处理
            }
        }
    }

    /**
     * 处理富文本“样式/类型”事件（富文本面板的所有按钮事件最终都会到这里）
     * 每执行一次操作，都会调用[notifyRichTextChange]进行保存回调
     */
    private fun handleStyleEvent(event: RichTextStyleActionEvent) {
        when (event) {
            // ==================== 段落级样式操作 ====================

            is RichTextStyleActionEvent.TodoToggled -> {
                // 切换待办事项：在当前行添加/移除可勾选的复选框
                areTodo?.setTodo()
            }

            is RichTextStyleActionEvent.NumberedListToggled -> {
                // 切换有序列表：在当前行添加/移除数字编号
                areListNumber?.setListNumber()
            }

            is RichTextStyleActionEvent.BulletedListToggled -> {
                // 切换无序列表：在当前行添加/移除项目符号
                areListBullet?.setListBullet()
            }

            // ==================== 字符级样式操作 ====================

            is RichTextStyleActionEvent.BoldToggled -> {
                // 切换粗体：对选中文本或光标位置应用/移除粗体格式
                areBold?.setBold()
            }

            is RichTextStyleActionEvent.ItalicToggled -> {
                // 切换斜体：对选中文本或光标位置应用/移除斜体格式
                areItalic?.setItalic()
            }

            is RichTextStyleActionEvent.UnderlineToggled -> {
                // 切换下划线：对选中文本或光标位置应用/移除下划线格式
                areUnderline?.setUnderLine()
            }

            // ==================== 暂时禁用的样式操作 ====================

            is RichTextStyleActionEvent.StrikethroughToggled -> {
                // 切换删除线(暂时禁用)
                areStrikethrough?.setStrikethrough()
            }

            is RichTextStyleActionEvent.AlignmentApplied -> {
                // 应用文本对齐方式(暂时禁用)
                areAlignment?.setAlignment(event.alignment)
            }

            is RichTextStyleActionEvent.IndentLeftApplied -> {
                // 增加左缩进(暂时禁用)
                areIndentLeft?.setIndentLeft()
            }

            is RichTextStyleActionEvent.IndentRightApplied -> {
                // 增加右缩进(暂时禁用)
                areIndentRight?.setIndentRight(null, true)
            }

            is RichTextStyleActionEvent.FontSizeApplied -> {
                // 设置字体大小(暂时禁用)
                areFontSize?.setFontSize(event.size)
            }

            is RichTextStyleActionEvent.FontColorApplied -> {
                // 设置字体颜色(暂时禁用)
                areFontColor?.setFontColor(event.color)
            }

            is RichTextStyleActionEvent.BackgroundColorApplied -> {
                // 设置字体背景颜色(暂时禁用)
                areBackgroundColor?.setBackgroundColor(event.color)
            }
        }
        // 触发内容变化回调，通知外部进行数据持久化
        notifyRichTextChange()

        // 确保编辑器获得焦点并显示键盘，方便用户继续编辑
        areEditText.requestFocusAndShowKeyboard()
    }

    /**
     * 通知富文本内容变化
     *
     * 当富文本内容或样式发生变化时，统一调用此方法进行回调通知。
     *
     * 功能：
     * 1. 获取当前编辑器的文本内容
     * 2. 提取当前的样式信息(RichTextStyleEntity)
     * 3. 通过回调函数通知外部(通常是ViewModel)进行数据持久化
     *
     * 调用时机：
     * - 用户应用任何样式操作后
     * - 待办事项状态变化时
     */
    private fun notifyRichTextChange() {
        val content = areEditText.text.toString()
        val style = areEditText.toRichTextStyleEntity()
        // 通知外部回调（ViewModel），进行数据持久化保存
        onRichTextChanged?.invoke(content, style)
    }

    /**
     * 处理编辑器点击事件
     *
     * 目前为空实现，预留用于未来扩展点击相关功能
     */
    override fun onClick(v: View?) {
        // 暂无特殊点击处理逻辑
    }

    /**
     * 注册待办事项消息监听
     *
     * 当编辑器附加到窗口时调用，用于监听待办事项相关的系统消息
     */
    private fun registerTodoMessage() {
        areTodo?.registerUpcomingMessage()
    }

    /**
     * 注销待办事项消息监听
     *
     * 当编辑器从窗口分离时调用，清理待办事项相关的消息监听
     */
    private fun unregisterTodoMessage() {
        areTodo?.unRegisterUpcomingMessage()
    }

    /**
     * 释放ViewHolder资源
     *
     * 完整清理ViewHolder持有的所有资源，包括协程、监听器、样式对象等。
     *
     * 清理步骤：
     * 1. 检查是否已释放，避免重复释放
     * 2. 取消事件监听协程
     * 3. 注销待办事项消息监听
     * 4. 销毁编辑器实例(如果尚未销毁)
     * 5. 清空所有样式对象引用
     *
     * 调用时机：
     * - ViewHolder生命周期结束时
     * - 页面销毁或切换时
     * - 内存回收时
     *
     * 注意：此方法应该在主线程调用，确保UI相关资源正确释放
     */
    fun onRelease() {
        // 防止重复释放
        if (isReleased) return
        isReleased = true

        Logger.d(TAG, "Releasing RichTextViewHolder for AREditText: ${areEditText.hashCode()}")

        // ==================== 协程资源清理 ====================
        // 取消事件监听协程，停止接收新的样式操作事件
        eventJob?.cancel()
        eventJob = null

        // ==================== 消息监听清理 ====================
        // 清理待办事项相关的消息监听
        unregisterTodoMessage()
        // 额外保险：直接清理待办样式对象的消息监听
        areTodo?.unRegisterUpcomingMessage()

        // ==================== 编辑器资源清理 ====================
        // 清理AREditText（作为备用清理，通常在RichTextController中已经调用了）
        if (!areEditText.isDestroyed) {
            areEditText.destroy()
        }

        // ==================== 样式对象引用清理 ====================
        // 清空所有样式对象引用，帮助GC回收内存
        areBold = null
        areItalic = null
        areUnderline = null
        areStrikethrough = null
        areAlignment = null
        areIndentRight = null
        areIndentLeft = null
        areListNumber = null
        areListBullet = null
        areFontSize = null
        areFontColor = null
        areBackgroundColor = null
        areTodo = null
    }

    companion object {
        // ==================== 布局相关常量 ====================

        /** 平板设备水平内边距(dp) */
        const val PADDING_HORIZONTAL_TABLET_DP = 48
        /** 手机设备水平内边距(dp) */
        const val PADDING_HORIZONTAL_PHONE_DP = 24
        /** 平板设备垂直内边距(dp) */
        const val PADDING_VERTICAL_TABLET_DP = 18
        /** 手机设备垂直内边距(dp) */
        const val PADDING_VERTICAL_PHONE_DP = 8

        // ==================== 文本样式常量 ====================

        /** 默认文本大小(sp) */
        const val TEXT_SIZE_SP = 16
        /** 默认行高(sp) */
        const val LINE_HEIGHT_SP = 28
    }
}