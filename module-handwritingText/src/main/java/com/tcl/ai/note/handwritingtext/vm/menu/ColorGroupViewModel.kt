package com.tcl.ai.note.handwritingtext.vm.menu

import android.util.Log
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.toComposeColor
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject


/**
 * 顶部三色菜单栏数据管理viewmodel
 */
class ColorGroupViewModel : ViewModel() {


    private val _colorGroupState = MutableStateFlow(ColorGroupState())
    val colorGroupState: StateFlow<ColorGroupState> = _colorGroupState.asStateFlow()



    private val defColos = listOf(
        Color(0xFF000000),
        Color(0xFFFFFFFF),
        Color(0xFF0243C8),
        Color(0xFF7EC443),
        Color(0xFFF8EB03),
        Color(0xFFFE2C1C),
        Color(0xFFBF35F8),
    )

    private val _collectColors = MutableStateFlow(defColos)
    val collectColors: StateFlow<List<Color>> = _collectColors.asStateFlow()


    fun updateRecentColor(colorIdx: Int, newColor: PenColor) {
        _colorGroupState.update { currentState ->
            currentState.copy(
                recentColors = currentState.recentColors.toMutableList().apply {
                    this[colorIdx] = newColor
                }
            )
        }
        saveRecentColors()
        collectColor(newColor.color)

    }

    fun updateSelColorIdx(colorIdx: Int) {
        saveCurrenColorIdx(colorIdx)
        _colorGroupState.update { currentState ->
            currentState.copy(selectedColorIdx = colorIdx)
        }
    }

    fun cancelSelectColor() {
        _colorGroupState.update { currentState ->
            currentState.copy(selectedColorIdx = -1)
        }
        saveCurrenColorIdx(-1)
    }

    private val maxRecentColors: Int = 7


    private fun collectColor(color: Color) {
        val list = _collectColors.value.toMutableList()
        list.remove(color)
        list.add(0, color)
        if (list.size > maxRecentColors) {
            list.removeAt(list.size - 1)
        }
        _collectColors.value = list
        saveCollectColors()
    }


    private fun loadCollectColors() {

        viewModelScope.launch {
            val json = AppDataStore.getStringData(::_collectColors.name, "")
            val colors = if (json.isNotEmpty()) {
                JSONArray(json).run {
                    List(length()) { i ->
                        getLong(i).toComposeColor()
                    }
                }
            } else {
                defColos
            }
            _collectColors.value = colors

        }
    }

    private fun saveCollectColors() {
        viewModelScope.launch {
            val json = JSONArray().apply {
                _collectColors.value.forEach { color ->
                    put(color.toArgb())
                }
            }.toString()
            AppDataStore.putStringData(::_collectColors.name, json)


        }
    }

    private fun saveRecentColors() {
        viewModelScope.launch {
            colorGroupState.value.saveRecentColors()
        }
    }

    private fun loadRecentColors() {
        viewModelScope.launch {
            val colors = colorGroupState.value.loadRecentColors()
            _colorGroupState.update { it.copy(recentColors = colors) }
        }
    }

    private fun saveCurrenColorIdx(colorIdx: Int) {
        viewModelScope.launch {
            AppDataStore.putInt(ColorGroupState::selectedColorIdx.name, colorIdx)
        }
    }
    private fun loadCurrenColorIdx(){
        viewModelScope.launch {
            val colorIdx = AppDataStore.getInt(ColorGroupState::selectedColorIdx.name, -1)
            _colorGroupState.update { it.copy(selectedColorIdx = colorIdx) }
        }
    }
    init {
        loadCollectColors()
        loadRecentColors()
        loadCurrenColorIdx()

    }

}

data class ColorGroupState(
    val recentColors: List<PenColor> = listOf(
        PenColor(color = Color(0xFF3D7DFE)),
        PenColor(color = Color(0xFFFE2C1C)),
        PenColor(color = Color(0xFFFFB916))
    ),
    val selectedColorIdx: Int = -1
) {
    fun getSelectedColor(colorIdx: Int): PenColor? {
        if (colorIdx < 0 || colorIdx >= recentColors.size) {
            return null
        }
        return recentColors[colorIdx]
    }

    fun getSelectedColor(): PenColor? {
        return getSelectedColor(selectedColorIdx)
    }

    suspend fun saveRecentColors() {
        val json = JSONArray().apply {
            recentColors.forEach { penColor ->
                val obj = JSONObject().apply {
                    put(penColor::color.name, penColor.color.toArgb())
                    put(penColor::alpha.name, penColor.alpha)
                }
                put(obj)
            }
        }.toString()
        AppDataStore.putStringData(::recentColors.name, json)
    }

    suspend fun loadRecentColors(): List<PenColor> {
        val json = AppDataStore.getStringData(::recentColors.name, "")
        val colors = if (json.isNotEmpty()) {
            JSONArray(json).run {
                List(length()) { i ->
                    val obj = getJSONObject(i)
                    PenColor(
                        color = obj.getLong(PenColor::color.name).toComposeColor(),
                        alpha = obj.getInt(PenColor::alpha.name)
                    )
                }
            }
        } else {
            recentColors
        }
        return colors

    }

}