package com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle

import android.R
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.SystemClock
import android.view.*
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import com.tcl.ai.note.handwritingtext.ui.richtext.base.BaseRichTextEditView
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCharWidth
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorHeight
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorPointF
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorWidth
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getLineEndAtCursorIndex
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getLineStartAtCursorIndex
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.dp2px
import kotlin.lazy
import kotlin.math.max


/**
 * 1. 如果是选区，只拦截事件，不显示自定义光标水滴
 * 2. 如果是插入光标，直接替换。
 */
@SuppressLint("ResourceType", "ClickableViewAccessibility")
abstract class BaseCursorHandleView(
    private val editText: BaseRichTextEditView,
    private var isDraggable: Boolean = true,
) : PopupWindow() {
    protected val cursorHandleImageView = ImageView(editText.context)
    protected val realWidth get() = cursorHandleImageView.drawable?.intrinsicWidth ?: 0
    protected val realHeight get() = cursorHandleImageView.drawable?.intrinsicHeight ?: 0
    protected val paddingHorizontal get() = (width - realWidth) / 2
    protected val paddingBottom get() = height - realHeight

    // 获取父view的大小，避免光标超出屏幕
    private var displayRect = Rect().also {
        (editText.parent as? View)?.getWindowVisibleDisplayFrame(it)
    }
    // 获取富文本的父View在屏幕的左上角
    private var editTextLocation = IntArray(2).also {
        (editText.parent as? View)?.getLocationInWindow(it)
    }
    protected val editTextLeft get() = editTextLocation[0]
    protected val editTextTop get() = editTextLocation[1]
    protected val editTextRight get() = editTextLocation[0] + editText.viewWidth
    protected val editTextBottom get() = editTextLocation[1] + editText.viewHeight

    init {
        isClippingEnabled = false
        isFocusable = false
        isOutsideTouchable = false

        editText.richTextWrapper.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
            refreshParentMeasure()
            updatePosition()
        }

        val linearLayout: LinearLayout = LinearLayout(editText.context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT,
            )
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
            addView(cursorHandleImageView)
        }

        // 根据布局观察得出来的值
        val maxEdge = maxOf(realWidth, realHeight, 50.dp2px)
        width = maxEdge
        height = maxEdge
        setContentView(linearLayout)
        contentView.setOnTouchListener { v, e -> dragEventListener.onTouch(v, e) }
    }

    /**
     * @param dragEventListener 处理拖动时间
     * @param isTouching 是否在触摸中
     */
    var isTouching = false
        private set
    protected val dragEventListener by lazy {
        object : View.OnTouchListener {
            private var isAlreadyScrollEdge = false
            private val autoScrollEdge = 50.dp2px
            private val eachScrollDistance = 5.dp2px
            // 一帧多少毫秒
            private val frameRate get() = 1000 / contentView.context.display.refreshRate
            // 用节流（throttle）机制减少 update 调用，比如每 16ms 一次
            private var lastUpdateTime = 0L
            // 上次边界滚动行的时间
            private var lastBoundaryRollingTime = 0L

            private var downRawX = 0f
            private var downRawY = 0f
            private var initialX = 0
            private var initialY = 0
            // activityWindowLocation为了解决分屏
            private val activityWindowLocation = IntArray(2)
            // popupViewLocation是获取拖动柄相对于富文本的位置
            private val popupViewLocation = IntArray(2)
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        isTouching = true
                        isAlreadyScrollEdge = false

                        editText.rootView.getLocationOnScreen(activityWindowLocation)
                        contentView.getLocationOnScreen(popupViewLocation)
                        initialX = popupViewLocation[0] - activityWindowLocation[0]
                        initialY = popupViewLocation[1] - activityWindowLocation[1]

                        downRawX = event.rawX
                        downRawY = event.rawY
                        Logger.d(TAG, "onTouch ACTION_DOWN, activityWindowLocation: ${activityWindowLocation.contentToString()}, popupViewLocation: ${popupViewLocation.contentToString()}, downRawX: $downRawX, downRawY: $downRawY")
                        return true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        val offsetX = event.rawX - downRawX
                        val offsetY = event.rawY - downRawY
                        var newX = initialX + offsetX
                        var newY = initialY + offsetY
                        // 防止超出屏幕
                        newX = newX.coerceIn(
                            (displayRect.left - activityWindowLocation[0] - width / 2).toFloat(),
                            (displayRect.right + width / 2).toFloat()
                        )
                        newY = newY.coerceIn(
                            (displayRect.top - activityWindowLocation[1] + editTextTop - realHeight).toFloat(),
                            (displayRect.bottom).toFloat()
                        )

                        if (isDraggable && SystemClock.elapsedRealtime() - lastUpdateTime > frameRate) {
                            // 每帧更新一次，性能优化，减缓光标不跟手
                            update(newX.toInt(), newY.toInt(), -1, -1, true)
                            // 到边界自动滚动
                            val leftEdge = newX - editTextLeft
                            val topEdge = newY - editTextTop
                            val rightEdge = editTextRight - newX - width
                            val bottomEdge = editTextBottom - newY - height
                            val translationX = when {
                                leftEdge < autoScrollEdge -> editText.translationX + eachScrollDistance
                                rightEdge < autoScrollEdge -> editText.translationX - eachScrollDistance
                                else -> editText.translationX
                            }
                            val translationY = when {
                                topEdge < autoScrollEdge -> editText.translationY + eachScrollDistance
                                bottomEdge < autoScrollEdge -> editText.translationY - eachScrollDistance
                                else -> editText.translationY
                            }
                            Logger.i(TAG, "onTouch ACTION_MOVE, leftEdge: $leftEdge, topEdge: $topEdge, rightEdge: $rightEdge, bottomEdge: $bottomEdge")
                            if (translationX != editText.translationX || translationY != editText.translationY) {
                                isAlreadyScrollEdge = true
                                editText.onScrollByCursorHandleStart?.invoke(translationX, translationY)
                            }
                            lastUpdateTime = SystemClock.elapsedRealtime()
                        }

                        val logicX = (newX - editText.translationX + width / 2) / editText.scaleX
                        val cursorHeight = editText.getCursorHeight()
                        val lineSpacing = editText.getCursorHeight(includeLineSpacing = true) - editText.getCursorHeight()
                        val logicY = (newY - editText.translationY - editTextTop - cursorHeight + lineSpacing) / editText.scaleY + realHeight
                        var cursorPosition = moveAndGetCursorPosition(logicX, logicY)
                        Logger.d(TAG, "onTouch ACTION_MOVE, logicX: $logicX, logicY: $logicY, cursorPosition: $cursorPosition")
//                        if (SystemClock.elapsedRealtime() - lastBoundaryRollingTime > 300) {
//                            // 让光标回到屏幕内,500ms滚动一行，不然会快速回到顶部
//                            editText.editRichCursorToScreen(
//                                index = cursorPosition
//                                    .coerceAtLeast(
//                                        editText.getLineStartAtCursorIndex(cursorPosition)
//                                    )
//                                    .coerceAtMost(
//                                        editText.getLineEndAtCursorIndex(cursorPosition)
//                                    ),
//                                verticalPadding = editText.getCursorHeight(
//                                    index = cursorPosition,
//                                    includeLineSpacing = true
//                                ),
//                                horizontalPadding = editText.getCharWidth(cursorPosition) * 2
//                            )
//                            lastBoundaryRollingTime = SystemClock.elapsedRealtime()
//                        }
                        return true
                    }

                    MotionEvent.ACTION_UP -> {
                        isTouching = false
                        // up事件复位拖动柄，避免错位
                        updatePosition()
                        if (isAlreadyScrollEdge) {
                            editText.onScrollByCursorHandleEnd?.invoke(editText.translationX, editText.translationY)
                        }
                        return true
                    }

                    MotionEvent.ACTION_CANCEL -> {
                        isTouching = false
                        if (isAlreadyScrollEdge) {
                            editText.onScrollByCursorHandleEnd?.invoke(editText.translationX, editText.translationY)
                        }
                        return false
                    }
                }
                return false
            }
        }
    }

    /**
     * 移动光标
     */
    protected abstract fun moveAndGetCursorPosition(eventX: Float, eventY: Float): Int

    /**
     * 修正光标拖动柄（水滴）位置，获取光标正下方位置(左对齐)
     */
    protected abstract fun calculateCursorPoint(): PointF

    /**
     * 计算拖动柄图标，修正的偏移量
     * 如：插入拖动柄，需要居中就要width / 2
     */
    protected abstract fun calculateHandleDrawableOffset(): PointF

    /**
     * 根据光标位置，放置水滴view
     */
    fun showOrUpdate(
        gravity: Int = Gravity.TOP or Gravity.START,
        onlyUpdate: Boolean = false,
    ) {
        if (editText?.isAttachedToWindow != true) {
            Logger.w(TAG, "showOrUpdate fail! view isn't attached to window")
            return
        }
        if (onlyUpdate && !isShowing) {
            Logger.i(TAG, "showOrUpdate fail! view isn't showing")
            return
        }
        if (isTouching) {
            Logger.i(TAG, "showOrUpdate fail! view isn't touching")
            return
        }

        // 获取光标正下方位置（左对齐）
        val point = calculateCursorPoint()
        Logger.i(TAG, "showOrUpdate, calculateCursorPoint, cursorPoint: $point")

        // 微调水滴的位置，让icon居中。现在icon是左对齐
        var x = point.x.toInt()
        var y = point.y.toInt()

        if (onlyUpdate || isShowing) {
            update(x, y, -1, -1, true)
            Logger.i(TAG, "showOrUpdate, update")
        } else {
            // showAsDropDown需要特殊处理一下y值, 创建光标
            y = y + height
            super.showAsDropDown(editText.rootView, x, y, gravity)
            Logger.i(TAG, "showOrUpdate, success")
        }

        // 超出边界，隐藏光标
        val calculateHandleDrawableOffset = calculateHandleDrawableOffset()
        if (!isTouching
            && (x < editTextLeft + calculateHandleDrawableOffset.x
                    || y < editTextTop + calculateHandleDrawableOffset.y
                    || x > editTextRight + calculateHandleDrawableOffset.x
                    || y > editTextBottom + calculateHandleDrawableOffset.y
                    )
        ) {
            Logger.i(TAG, "showOrUpdate, hide handle cursor! cursorPoint is out of screen")
            // 放置到屏幕外，起到隐藏作用。避免拦截事件
            update(-width, -height, -1, -1)
            return
        }
    }

    /**
     * 只更新光标位置，不创建光标
     */
    open fun updatePosition() {
        showOrUpdate(Gravity.TOP or Gravity.START, true)
    }

    private fun refreshParentMeasure() {
        displayRect = Rect().also {
            (editText.parent as? View)?.getWindowVisibleDisplayFrame(it)
        }
        editTextLocation = IntArray(2).also {
            (editText.parent as? View)?.getLocationInWindow(it)
        }
        Logger.i(TAG, "refreshParentMeasure, displayRect: $displayRect, editTextLocation: ${editTextLocation.get(0)}, ${editTextLocation.get(1)}")
    }

    /**
     * 使用函数[showOrUpdate]代替
     */
    @Deprecated("请使用showOrUpdate方法替换")
    override fun showAsDropDown(anchor: View?, xoff: Int, yoff: Int, gravity: Int) {
        throw IllegalAccessException("请使用showOrUpdate(cursorIndex: Int, gravity: Int)方法替换")
    }

    // 添加震动效果
    protected fun vibrate() {
        editText.performHapticFeedback(HapticFeedbackConstants.TEXT_HANDLE_MOVE)
    }

    protected fun getCursorDrawable(context: Context, cursorType: CursorType) = BaseCursorHandleView.getCursorDrawable(context, cursorType)

    companion object {
        private const val TAG = "EditCursorView"
        private val cursorResMap = HashMap<Int, Drawable?>(3)
        enum class CursorType { LEFT_SELECTION, INSERT, RIGHT_SELECTION }

        // 获取系统光标资源
        private fun getCursorDrawable(context: Context, cursorType: CursorType) = when (cursorType) {
            CursorType.LEFT_SELECTION -> {
                cursorResMap.getOrPut(R.attr.textSelectHandleLeft) {
                    context.obtainStyledAttributes(intArrayOf(R.attr.textSelectHandleLeft)).use {
                        trimDrawableTransparent(it.getDrawable(0), context)
                    }
                }
            }

            CursorType.INSERT -> {
                cursorResMap.getOrPut(R.attr.textSelectHandle) {
                    context.obtainStyledAttributes(intArrayOf(R.attr.textSelectHandle)).use {
                        trimDrawableTransparent(it.getDrawable(0), context)
                    }
                }
            }

            CursorType.RIGHT_SELECTION -> {
                cursorResMap.getOrPut(R.attr.textSelectHandleRight) {
                    context.obtainStyledAttributes(intArrayOf(R.attr.textSelectHandleRight)).use {
                        trimDrawableTransparent(it.getDrawable(0), context)
                    }
                }
            }
        }

        // 裁剪透明区域
        private fun trimDrawableTransparent(drawable: Drawable?, context: Context): Drawable? {
            if (drawable == null) return drawable
            Logger.d(TAG, "trimDrawableTransparent, raw, width: ${drawable.intrinsicWidth}, height: ${drawable.intrinsicHeight}")
            val bmp = Bitmap.createBitmap(
                drawable.intrinsicWidth,
                drawable.intrinsicHeight,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bmp)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)

            val w = bmp.width
            val h = bmp.height
            val pixels = IntArray(w * h)
            bmp.getPixels(pixels, 0, w, 0, 0, w, h)

            var left = w;
            var right = 0;
            var top = h;
            var bottom = 0
            for (y in 0 until h) {
                for (x in 0 until w) {
                    if ((pixels[y * w + x] ushr 24) > 0) {
                        if (x < left) left = x
                        if (x > right) right = x
                        if (y < top) top = y
                        if (y > bottom) bottom = y
                    }
                }
            }
            // 防止图片全透明
            if (right < left || bottom < top) return drawable

            val outBmp = Bitmap.createBitmap(bmp, left, top, right - left + 1, bottom - top + 1)
            Logger.d(TAG, "trimDrawableTransparent, new, width: ${outBmp.width}, height: ${outBmp.height}")
            return BitmapDrawable(context.resources, outBmp)
        }
    }
}