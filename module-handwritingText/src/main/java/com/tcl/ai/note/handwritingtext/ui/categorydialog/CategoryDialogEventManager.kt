package com.tcl.ai.note.handwritingtext.ui.categorydialog

import androidx.compose.ui.graphics.Color
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 分类弹窗事件管理器
 * 用于传递分类弹窗的事件
 */
object CategoryDialogEventManager {
    // 分类弹窗事件
    private val _categoryDialogEvents = MutableSharedFlow<CategoryDialogEvent>()
    val categoryDialogEvents = _categoryDialogEvents.asSharedFlow()
    // 分类弹窗回调事件
    private val _categoryDialogCallBackEvents = MutableSharedFlow<CategoryDialogCallBackEvent>()
    val categoryDialogSuccessEvents = _categoryDialogCallBackEvents.asSharedFlow()

    suspend fun sendCategoryDialogEvent(event: CategoryDialogEvent) {
        _categoryDialogEvents.emit(event)
    }
    /*
    * 重命名分类
     */
    suspend fun sendRenameCategoryEvent( id: String, name: String?, colorIndex: Int, color: Color? = null,icon:Int?,noteCounts :Int) {
        sendCategoryDialogEvent(
            CategoryDialogEvent(
                type = CategoryDialogType.EDIT_CATEGORY,
                dialogCategory = DialogCategory(
                    categoryId = id.toLongOrNull() ?: 1L,
                    noteCounts = noteCounts,
                    name = name ?: "",
                    colorIndex = colorIndex,
                    color = color,
                    icon = icon
                )
            )
        )
    }
    /**
     * 发送分类弹窗回调事件
     */
    suspend fun sendCategoryDialogCallBackEvent(event: CategoryDialogCallBackEvent) {
        _categoryDialogCallBackEvents.emit(event)
    }
}

data class CategoryDialogEvent(
    val type: CategoryDialogType=CategoryDialogType.NEW_CATEGORY,
    val dialogCategory: DialogCategory? = null,
    val dialogNote: DialogNote? = null,
    val dialogNotes: List<DialogNote> = emptyList(),
    val noteCategories:List<NoteCategory> = emptyList(),
    val categoryId: Long = 1, // 分类ID
    val isPreviewMode:Boolean = false //是否在富文本编辑页触发移动

)

/**
 * 分类弹窗回调事件
 */
sealed class CategoryDialogCallBackEvent(
    open val categoryId: String
) {
    data class OnCategoryCreatedCallBack(
        override val categoryId: String
    ) : CategoryDialogCallBackEvent(categoryId)

    data class OnCategoryDialogDismiss(
        override val categoryId: String=""
    ) : CategoryDialogCallBackEvent(categoryId)

    data class OnCategoryNoteMoved(
        override val categoryId: String
    ) : CategoryDialogCallBackEvent(categoryId)

    //富文本编辑页移动note至新分类
    data class OnNoteMovedSuccessful(
        override val categoryId: String
    ) : CategoryDialogCallBackEvent(categoryId)
}