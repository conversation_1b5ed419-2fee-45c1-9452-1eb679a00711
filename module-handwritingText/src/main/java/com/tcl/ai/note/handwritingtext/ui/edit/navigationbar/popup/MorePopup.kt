package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.popup

import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.unit.Density
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogEvent
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogEventManager
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogType
import com.tcl.ai.note.handwritingtext.ui.categorydialog.DialogNote
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils.PopupOffsetUtils
import com.tcl.ai.note.handwritingtext.ui.popup.FocusedBounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MoreActionContent
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import kotlinx.coroutines.launch

@Composable
fun MorePopupComponent(
    isVisible: Boolean,
    noteId: Long,
    categoryId: Long,
    enableFingerDrawing: Boolean,
    density: Density,
    onDismiss: () -> Unit,
    onDelete: () -> Unit,
    onPageSettings: () -> Unit,
    onFingerDrawing: (isOff: Boolean) -> Unit
) {
    if (!isVisible) return
    
    val coroutineScope = rememberCoroutineScope()
    
    val morePopupOffset = PopupOffsetUtils.calculateMorePopupOffset(TclTheme.dimens, density)

    FocusedBounceScalePopup(
        onDismissRequest = onDismiss,
        offset = morePopupOffset,
        alignment =  Alignment.TopEnd,
        enterTransformOrigin = TransformOrigin(1f, 0f),
        exitTransformOrigin = TransformOrigin(1f, 0f),
    ) { closePopup ->
        MoreActionContent(
            enableFingerDrawing = enableFingerDrawing,
            onPageSettings = {
                closePopup()
                onPageSettings()
            },
            onMoveNote = {
                showMoveNoteDialog(noteId, categoryId, coroutineScope)
                closePopup()
            },
            onShareNote = { },
            onDelete = {
                closePopup()
                onDelete()
            },
            onDismissPopup = closePopup,
            onFingerDrawing = onFingerDrawing
        )
    }
}

private fun showMoveNoteDialog(noteId: Long, categoryId: Long, coroutineScope: kotlinx.coroutines.CoroutineScope) {
    coroutineScope.launch {
        val dialogNotes = listOf(
            DialogNote(id = noteId, categoryId = categoryId, name = "", content = "")
        )
        CategoryDialogEventManager.sendCategoryDialogEvent(
            CategoryDialogEvent(
                type = CategoryDialogType.MOVE_NOTE_TO_CATEGORY,
                dialogNotes = dialogNotes,
                categoryId = categoryId,
                isPreviewMode = true
            )
        )
    }
}