package com.tcl.ai.note.handwritingtext.database.entity

import android.net.Uri
import androidx.compose.ui.text.input.TextFieldValue
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Ignore
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.tcl.ai.note.handwritingtext.database.convertor.NoteContentConvertor
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity

// 数据库实现（使用Room）
@Entity(tableName = DBConst.TABLE_NAME_NOTES)
@TypeConverters(NoteContentConvertor::class)
data class Note(
    @PrimaryKey(autoGenerate = true)
    val noteId: Long = 0,
    //@PrimaryKey val id: String = UUID.randomUUID().toString(),
    var title: String = "",
    var content: String = "", // 完整内容字段
    val contents: List<EditorContent> = emptyList(),
    var summary: String? = "", // 摘要字段（完整内容前50字符摘要，方便列表展示）
    @ColumnInfo(name = "first_picture")  // 首图字段
    var firstPicture: String? = null,
    @ColumnInfo(name = "handwriting_thumbnail")  // 手绘缩略图
    val handwritingThumbnail: String? = null,
    var hasAudio: Boolean? = false, // 是否有录音
    var categoryId: Long = 1, // 未分类
    var createTime: Long? = System.currentTimeMillis(),
    var modifyTime: Long? = createTime,
    var bgMode: BgMode = BgMode.none,
    var bgColor: Long = Skin.defColor,
    /** 新增字段 **/
    var deleteFlag: Boolean = false, // 逻辑删除标记
    var isShow: Boolean = false,     // 是否显示在首页
    // 记住最后编辑位置的字段
    var lastEditOffsetX: Float = 0f, // 最后编辑时的X轴偏移
    var lastEditOffsetY: Float = 0f, // 最后编辑时的Y轴偏移
    var lastEditScale: Float = 1.0f, // 最后编辑时的缩放比例
    var lastEditCursorPosition: Int = 0 // 最后编辑时的光标位置
) {
    val audioPaths
        get() = contents
            .filterIsInstance<EditorContent.AudioBlock>()
            .map { it.audioPath }
}

/**
 * 列表专用数据类
 * 首页使用 查询出了分类的名称和颜色索引
 */
@TypeConverters(NoteContentConvertor::class)
data class NoteListItem(
    val noteId: Long,
    val title: String,
    val summary: String?,
    val content: String?,
    //样式
    val contents: List<EditorContent>? = null,
    @ColumnInfo(name = "first_picture")
    val firstPicture: String?,
    @ColumnInfo(name = "handwriting_thumbnail")  // 手绘缩略图
    val handwritingThumbnail: String?,
    val hasAudio: Boolean? = false,
    val categoryId: Long,
    val categoryName: String?=null,//联表查询分类名称
    var categoryColorIndex: Int? = 7,//联表查询分类颜色索引
    val createTime: Long?,
    val modifyTime: Long?,
    var bgMode: BgMode = BgMode.none,
    var bgColor: Long = Skin.defColor
)

/**
 * 富文本内容
 */
@Entity(
    tableName = DBConst.TABLE_NAME_CONTENTS,
    foreignKeys = [
        ForeignKey(
            entity = Note::class,
            parentColumns = ["noteId"],
            childColumns = ["noteId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["noteId"])] // 添加索引
)
@Deprecated("原用于存储富文本，现转用Notes存储")
data class Content(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val noteId: Long, // 内容所属的笔记 ID
    val type: ContentType, // 内容类型（文字、图片、待办事项）
    @ColumnInfo(name = "content_data")
    val contentData: String,  // 存储序列化的文本内容
    @ColumnInfo(name = "styles")
    val styles: String,        // 存储序列化的样式信息
    val order: Int // 顺序标识
)

@Entity(tableName = DBConst.TABLE_NAME_CATEGORIES)
data class NoteCategory(
    @PrimaryKey(autoGenerate = true)
    val categoryId: Long = 0,
    var name: String = "",
    var colorIndex: Int = 7,
    var isRename: Boolean = false, // name是否更改过
    var createTime: Long? = null,
    var modifyTime: Long? = null
) {
    @Ignore
    var noteCounts: Int = 0

    @Ignore
    var icon: Int = 0
}

/**
 * 段落样式
 */
enum class ParagraphStyle {
    NONE, NUMBERED, BULLETED
}

/**
 * 段落样式
 * TEXT:普通文本
 * NUMBERED_LIST：有序列表
 * BULLETED_LIST：无序列表
 * TODO_ITEM：待办事项
 */
enum class ParagraphType {
    TEXT, NUMBERED_LIST, BULLETED_LIST, TODO_ITEM
}

enum class ImageBlockScaleMode {
    Large, Origin, Small
}

/**
 * 富文本区内容
 */
sealed class EditorContent {
    data class TextBlock(
        var text: TextFieldValue,
        var paragraphStyle: ParagraphStyle = ParagraphStyle.NONE
    ) : EditorContent()

    data class ImageBlock(val uri: Uri, val scaleMode: ImageBlockScaleMode = ImageBlockScaleMode.Origin) : EditorContent()
    data class TodoBlock(var text: TextFieldValue, var isDone: Boolean) : EditorContent()
    data class AudioBlock(val audioPath: String, var audioDuration: Long) : EditorContent()
    data class RichTextV2(val richTextStyleEntity: RichTextStyleEntity) : EditorContent()
}