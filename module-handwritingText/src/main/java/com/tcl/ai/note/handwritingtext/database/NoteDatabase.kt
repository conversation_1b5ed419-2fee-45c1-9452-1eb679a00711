package com.tcl.ai.note.handwritingtext.database

import android.content.ContentValues
import android.content.Context
import android.database.SQLException
import android.database.sqlite.SQLiteDatabase
import android.util.Pair
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.tcl.ai.note.handwritingtext.database.entity.DBConst
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.dao.DrawDao
import com.tcl.ai.note.handwritingtext.database.entity.Content
import com.tcl.ai.note.handwritingtext.database.entity.Draw
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.OldCategoryColors

@Database(
    entities = [Note::class, Content::class, NoteCategory::class, Draw::class],
    version = 3,
    autoMigrations = [],
    exportSchema = true
)

abstract class NoteDatabase : RoomDatabase() {

    abstract fun noteDao(): NoteDao
    abstract fun contentDao(): ContentDao
    abstract fun categoryDao(): CategoryDao
    abstract fun drawDao(): DrawDao

    companion object {

        @Volatile
        private var INSTANCE: NoteDatabase? = null

        @JvmStatic
        fun getInstance(context: Context): NoteDatabase {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: buildDataBase(context).also { INSTANCE = it }
            }
        }

        // TODO: 下次升级时请移除废弃的Content表
        private fun buildDataBase(context: Context): NoteDatabase {
            val MIGRATION_1_2 = object : Migration(1, 2) {
                override fun migrate(db: SupportSQLiteDatabase) {
                    Logger.i("NoteDatabase", "beginTransaction start")
                    db.beginTransaction()
                    try {
                        // 备份原始数据
                        db.execSQL("CREATE TABLE ${DBConst.TABLE_NAME_CATEGORIES}_backup AS SELECT * FROM ${DBConst.TABLE_NAME_CATEGORIES}")

                        // 更新颜色索引
                        db.execSQL("""
                UPDATE ${DBConst.TABLE_NAME_CATEGORIES} 
                SET colorIndex = CASE
                    WHEN colorIndex = ${OldCategoryColors.NONE_COLOR} THEN ${CategoryColors.NONE_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.YELLOW_COLOR} THEN ${CategoryColors.YELLOW_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.ORANGE_COLOR} THEN ${CategoryColors.ORANGE_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.RED_COLOR} THEN ${CategoryColors.PINK_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.PURPLE_COLOR} THEN ${CategoryColors.PURPLE_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.BLUE_COLOR} THEN ${CategoryColors.BLUE_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.BLACK_COLOR} THEN ${CategoryColors.GREEN_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.GREEN_COLOR} THEN ${CategoryColors.GREEN_COLOR}
                    WHEN colorIndex = ${OldCategoryColors.GRAY_COLOR} THEN ${CategoryColors.PINK_COLOR}
                    ELSE colorIndex
                END
            """.trimIndent())

                        // Note表新增 deleteFlag 默认 0（false）
                        db.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_NOTES} ADD COLUMN deleteFlag INTEGER NOT NULL DEFAULT 0")
                        // Note表新增 isShow 默认 0（false）
                        db.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_NOTES} ADD COLUMN isShow INTEGER NOT NULL DEFAULT 0")

                        // 老数据升级——让历史笔记全部首页可见
                        db.execSQL("UPDATE ${DBConst.TABLE_NAME_NOTES} SET isShow = 1")

                        db.setTransactionSuccessful()
                        Logger.i("NoteDatabase", "setTransactionSuccessful end")
                    } catch (e: Exception) {
                        Logger.e("NoteDatabase", "Category color migration failed: ${e.message}")

                        try {
                            // 删除可能已部分修改的表
                            db.execSQL("DROP TABLE IF EXISTS ${DBConst.TABLE_NAME_CATEGORIES}")

                            // 恢复备份
                            db.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_CATEGORIES}_backup RENAME TO ${DBConst.TABLE_NAME_CATEGORIES}")
                        } catch (innerEx: Exception) {
                            Logger.e("NoteDatabase", "Restore backup failed: ${innerEx.message}")
                        }

                        throw e
                    } finally {
                        try {
                            // 清理备份
                            db.execSQL("DROP TABLE IF EXISTS ${DBConst.TABLE_NAME_CATEGORIES}_backup")
                        } catch (e: SQLException) {
                            Logger.w("NoteDatabase", "Failed to clean the backup table: ${e.message}")
                        }
                        db.endTransaction()
                    }
                }
            }

            val MIGRATION_2_3 = object : Migration(2, 3) {
                override fun migrate(database: SupportSQLiteDatabase) {
                    Logger.i("NoteDatabase", "Migration 2->3: Adding last edit position fields")
                    database.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_NOTES} ADD COLUMN lastEditOffsetX REAL NOT NULL DEFAULT 0.0")
                    database.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_NOTES} ADD COLUMN lastEditOffsetY REAL NOT NULL DEFAULT 0.0")
                    database.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_NOTES} ADD COLUMN lastEditScale REAL NOT NULL DEFAULT 1.0")
                    database.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_NOTES} ADD COLUMN lastEditCursorPosition INTEGER NOT NULL DEFAULT 0")
                }
            }

            return Room.databaseBuilder(context, NoteDatabase::class.java, DBConst.DB_NAME)
                .addCallback(object : Callback() {
                    override fun onCreate(db: SupportSQLiteDatabase) {
                        super.onCreate(db)
                        //初始化数据
                        initNoteCategoryData(db, context)
                    }
                })
                .setJournalMode(JournalMode.TRUNCATE) // 禁用WAL模式(测试用)
                .addMigrations(MIGRATION_1_2, MIGRATION_2_3)
                .build()
        }

        /**
         * 填充 Category 表的数据，初始创建完表调用
         */
        private fun initNoteCategoryData(db: SupportSQLiteDatabase, context: Context) {
            val categories: MutableList<Pair<String, Int>> = ArrayList()
            categories.add(
                Pair(
                    context.getString(R.string.database_preset_category_none),
                    CategoryColors.NONE_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(R.string.database_preset_category_education),
                    CategoryColors.GREEN_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(R.string.database_preset_category_work),
                    CategoryColors.BLUE_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(R.string.database_preset_category_travel),
                    CategoryColors.PURPLE_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(R.string.database_preset_shopping_list),
                    CategoryColors.PINK_COLOR
                )
            )


            for (i in categories.indices) {
                val category = categories[i]
                val currentTime = System.currentTimeMillis() + i
                val values = ContentValues()
                values.put("name", category.first)
                values.put("createTime", currentTime)
                values.put("modifyTime", currentTime)
                values.put("colorIndex", category.second)
                values.put("isRename", false)
                db.insert(DBConst.TABLE_NAME_CATEGORIES, SQLiteDatabase.CONFLICT_REPLACE, values)
            }
        }

    }
}