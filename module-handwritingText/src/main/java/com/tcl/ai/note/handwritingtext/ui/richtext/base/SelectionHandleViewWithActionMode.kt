package com.tcl.ai.note.handwritingtext.ui.richtext.base

import android.graphics.Rect
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.View
import com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle.SelectionLeftCursorHandleView
import com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle.SelectionRightCursorHandleView
import com.tcl.ai.note.utils.Logger

class SelectionHandleViewWithActionMode(val editView: BaseRichTextEditView) {
    // 左选区水滴
    private val leftSelectionCursorHandle = SelectionLeftCursorHandleView(editView)
    // 右选区水滴
    private val rightSelectionCursorHandle = SelectionRightCursorHandleView(editView)
    // 是否正在显示选区菜单栏
    private var curActionMode: ActionMode? = null

    // 是否在显示菜单栏
    val isShowingActionMode get() = curActionMode != null
    // 是否在显示光标拖动柄,。选区的左右拖动柄总是同时出现的
    val isShowingCursorHandle get() = leftSelectionCursorHandle.isShowing || rightSelectionCursorHandle.isShowing

    private val selectionActionModeCallback = object : ActionMode.Callback2() {
        override fun onGetContentRect(mode: ActionMode?, view: View?, outRect: Rect?) {
            super.onGetContentRect(mode, view, outRect)
            if (view == null) return

            val startCursorPointF = editView.getCursorPointF(index = editView.selectionStart, includeScale = false)
            val endCursorPointF = editView.getCursorPointF(index = editView.selectionEnd, includeScale = false)
            when {
                (startCursorPointF.y == endCursorPointF.y) -> {
                    // 左右拖动柄在同一行
                    outRect?.set(
                        startCursorPointF.x.toInt(),
                        startCursorPointF.y.toInt(),
                        endCursorPointF.x.toInt(),
                        endCursorPointF.y.toInt()
                    )
                }

                else -> {
                    // 左右拖动柄不在同一行
                    outRect?.set(
                        0,
                        startCursorPointF.y.toInt(),
                        editView.width,
                        endCursorPointF.y.toInt() + editView.lineHeight,
                    )
                }
            }
            Logger.i(TAG, "customInsertionActionModeCallback onGetContentRect: view: $view, outRect: $outRect")
        }

        override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            Logger.i(TAG, "customSelectionActionModeCallback onCreateActionMode")
            val systemContext = editView.context.createPackageContext("android", 0)
            val cutStr = systemContext.getString(android.R.string.cut)
            val copyStr = systemContext.getString(android.R.string.copy)
            val pasteStr = systemContext.getString(android.R.string.paste)
            val selectAllStr = systemContext.getString(android.R.string.selectAll)
            val autofillStr = systemContext.getString(android.R.string.autofill)

            // 剪切
            menu?.add(0, android.R.id.cut, 0, cutStr)
            // 复制
            menu?.add(0, android.R.id.copy, 0, copyStr)
            if (editView.canPaste()) {
                // 添加粘贴 Paste
                menu?.add(0, android.R.id.paste, 0, pasteStr)
            }
            // 添加全选 Select All
            menu?.add(0, android.R.id.selectAll, 1, selectAllStr)
            if (editView.canRequestAutofill()) {
                // 添加Auto Fill
                menu?.add(0, android.R.id.autofill, 2, autofillStr)
            }
            return true
        }

        override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            Logger.i(TAG, "customSelectionActionModeCallback onPrepareActionMode")
            return true
        }

        override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
            when (item?.itemId) {
                android.R.id.cut -> {
                    if (editView.selectionStart > editView.selectionEnd) {
                        Logger.i(TAG, "cut: selection string index out")
                        return false
                    }
                    editView.onTextContextMenuItem(item.itemId)
                    hideSelectionActionMode()
                }

                android.R.id.copy -> {
                    if (editView.selectionStart > editView.selectionEnd) {
                        Logger.i(TAG, "copy: selection string index out")
                        return false
                    }
                    editView.onTextContextMenuItem(item.itemId)
                }

                android.R.id.selectAll -> {
                    editView.onTextContextMenuItem(item.itemId)
                    updateSelectionCursorHandle()
                }

                android.R.id.paste -> {
                    editView.onTextContextMenuItem(item.itemId)
                    hideSelectionActionMode()
                }

                android.R.id.autofill -> {
                    editView.onTextContextMenuItem(item.itemId)
                    hideSelectionActionMode()
                }
            }
            return true
        }

        override fun onDestroyActionMode(mode: ActionMode?) {
        }
    }

    init {
        editView.customSelectionActionModeCallback = selectionActionModeCallback
        leftSelectionCursorHandle.onLeftSelectionMovePosition = {
            hideSelectionActionMode()
            updateSelectionCursorHandle(updateLeftHandle = true, updateRightHandle = false)
        }
        leftSelectionCursorHandle.onLeftSelectionMovePositionEnd = {
            if (editView.selectionStart > editView.selectionEnd) {
                editView.setSelection(editView.selectionEnd, editView.selectionStart)
            }
            showSelectionCursorHandle()
        }

        rightSelectionCursorHandle.onRightSelectionMovePosition = {
            hideSelectionActionMode()
            updateSelectionCursorHandle(updateLeftHandle = false, updateRightHandle = true)
        }
        rightSelectionCursorHandle.onRightSelectionMovePositionEnd = {
            if (editView.selectionStart > editView.selectionEnd) {
                editView.setSelection(editView.selectionEnd, editView.selectionStart)
            }
            showSelectionCursorHandle()
        }
    }

    // 显示插入光标拖动柄的菜单栏, 也就是粘贴，全选功能。
    private fun showSelectionActionMode() {
        if (curActionMode != null) {
            updateSelectionCursorHandle()
            return
        }
        curActionMode = editView.startActionMode(selectionActionModeCallback, ActionMode.TYPE_FLOATING)
    }

    /**
     * 更新菜单栏位置，同时更新选区拖动柄的位置
     */
    private fun updateSelectionActionMode() {
        // 更新菜单栏位置
        curActionMode?.invalidateContentRect()
    }

    // 隐藏插入光标拖动柄的菜单栏, 也就是粘贴，全选功能。
    // 因为插入拖动柄是自己执行了startActionMode，所以要自己隐藏
    private fun hideSelectionActionMode() {
        curActionMode?.finish()
        curActionMode = null
    }

    /**
     * 显示选区光标拖动柄
     *
     * 受customSelectionActionModeCallback的显示影响
     */
    fun showSelectionCursorHandle() {
        leftSelectionCursorHandle?.showOrUpdate()
        rightSelectionCursorHandle?.showOrUpdate()
        showSelectionActionMode()
    }

    fun updateSelectionCursorHandle(updateLeftHandle: Boolean = true, updateRightHandle: Boolean = true) {
        if (updateLeftHandle) {
            leftSelectionCursorHandle?.updatePosition()
        }
        if (updateRightHandle) {
            rightSelectionCursorHandle?.updatePosition()
        }
        updateSelectionActionMode()
    }

    /**
     * 隐藏选区光标拖动柄
     *
     * 受customSelectionActionModeCallback的显示影响
     */
    fun hideSelectionCursorHandle() {
        leftSelectionCursorHandle?.dismiss()
        rightSelectionCursorHandle?.dismiss()
        hideSelectionActionMode()
    }

    companion object {
        private const val TAG = "SelectionHandleViewWithActionMode"
    }
}