package com.tcl.ai.note.handwritingtext.vm.draw

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.PointF
import android.graphics.RectF
import android.view.View
import androidx.annotation.ColorInt
import androidx.annotation.IntRange
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sunia.penengine.sdk.operate.touch.DeleteProp
import com.sunia.penengine.sdk.operate.touch.EraserType
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.sunia.penengine.sdk.operate.touch.PenType
import com.tcl.ai.note.handwritingtext.sunia.history.DrawEditOperation
import com.tcl.ai.note.handwritingtext.sunia.SuniaDrawViewController
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup.EraserMode
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.template.bean.JournalContentInfo
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.SPUtils
import com.tcl.ai.note.utils.delegate
import com.tcl.ai.note.utils.delegateViewFunc
import com.tcl.ai.note.utils.delegateViewSuspendFunc
import com.tcl.ai.note.utils.delegateViewSuspendFunc
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import com.sunia.penengine.sdk.operate.touch.EraserMode as SuniaEraserMode

open class SuniaDrawViewModel : ViewModel() {

    private var mNoteIdState = MutableStateFlow<Long?>(null)
    val noteIdState = mNoteIdState.asStateFlow()
    val noteId get() = noteIdState.value
    private val defPenProp = PenProp(
        PenType.PEN_FOUNTAIN.value,
        Color.BLACK,
        8f,
        100
    )

    // 默认橡皮擦属性 (Area eraser 模式，55px)
    private val defDeleteProp = DeleteProp(
        SuniaEraserMode.PointMode, // Area eraser 模式
        EraserType.CIRCLE,
        55f, // Area eraser 默认 55px (范围 10-100px)
        PointF()
    )

    // 显示大小，用于UI指示器
    private var displayEraserSize = 55f
    private var displayEraserMode = SuniaEraserMode.PointMode
    
    // 当前是否处于橡皮擦模式
    private var isCurrentlyInEraserMode = false

    // 是否正在加载
    private val mDrawLoadFinishState = MutableStateFlow(false)
    private var isDrawLoadFinish by mDrawLoadFinishState.delegate()
    val drawLoadFinishState = mDrawLoadFinishState.asStateFlow()
    suspend fun awaitDrawLoadFinish() = drawLoadFinishState.filter { it }.first()

    // 手绘可视大小
    private val mDrawStokeVisibleRectState = MutableStateFlow(RectF())
    val drawStokeVisibleRectState = mDrawStokeVisibleRectState.asStateFlow()
    var drawStokeVisibleRect by mDrawStokeVisibleRectState.delegate()

    // 笔刷属性
    private val mPenPropState = MutableStateFlow(defPenProp)
    val penPropState  = mPenPropState.asStateFlow()

    // 橡皮擦属性状态(擦除范围、擦除类型)
    private val mDeletePropState = MutableStateFlow(defDeleteProp)
    val deletePropState = mDeletePropState.asStateFlow()
    
    // 显示用的橡皮擦指示器属性状态（不受缩放影响，用于弹框预览和按压指示器）
    private val mDisplayEraserPropState = MutableStateFlow(defDeleteProp)
    val displayEraserPropState = mDisplayEraserPropState.asStateFlow()

    // undo/redo状态
    private val _canUndoState = MutableStateFlow(false)
    val canUndoState = _canUndoState.asStateFlow()
    val canUndo get() = canUndoState.value

    private val _canRedoState = MutableStateFlow(false)
    val canRedoState = _canRedoState.asStateFlow()
    val canRedo get() = canRedoState.value

    // 触摸位置状态 - 用于橡皮擦按压指示器
    private val _currentTouchPositionState = MutableStateFlow<androidx.compose.ui.geometry.Offset?>(null)
    val currentTouchPositionState = _currentTouchPositionState.asStateFlow()

    // 防误触功能，也就是不允许手指绘画
    private val mEnableFingerDrawingState = MutableStateFlow(
        SPUtils.getBoolean(
            key = SP_KEY_FINGER_DRAW_ENABLED,
            defValue = !isTablet
        )
    )
    val enableFingerDrawingState = mEnableFingerDrawingState.asStateFlow()

    fun changeFingerDrawing(enabled: Boolean) {
        mEnableFingerDrawingState.update { enabled }
        SPUtils.setSync(
            key = SP_KEY_FINGER_DRAW_ENABLED,
            value = enabled
        )
    }

    private val mInsertBitmapSharedFlow = MutableSharedFlow<InsertBitmapEvent>()
    val insertBitmapSharedFlow = mInsertBitmapSharedFlow.asSharedFlow()

    // 图片数量
    private val mImageCountState = MutableStateFlow(0)
    val imageCountState = mImageCountState.asStateFlow()
    // 显示区域大小，用于计算图片插入位置
    private val mShowRectState = MutableStateFlow(RectF(0f, 0f, 0f, 0f))
    val showRectState = mShowRectState.asStateFlow()

    // 一笔成型状态
    private val mShapeRecognizeState = MutableStateFlow(false)
    val shapeRecognizeState  = mShapeRecognizeState.asStateFlow()

    // 套索状态
    private val mLassoState = MutableStateFlow(false)
    val lassoState = mLassoState.asStateFlow()

    // 套索编辑状态
    private val mLassoEditState = MutableStateFlow<LassoEditEvent?>(null)
    val lassoEditState = mLassoEditState.asStateFlow()

    // 文本模式下长按的点
    private val _onClickPointState = MutableSharedFlow<PointF>()
    val onClickPointState = _onClickPointState.asSharedFlow()
    // 正在插入图片
    var isInsertingImage = false

    // 套索状态
    private val mInsertImageScrollDistanceState = MutableStateFlow<InsertBitmapEvent?>(null)
    val insertImageScrollDistanceState = mInsertImageScrollDistanceState.asStateFlow()


    /**
     * 监听是否有文本变化、格式转换操作
     *
     * 注：不包含undo和redo导致的文本变化
     */
    private val mUndoRedoOperationShared = MutableSharedFlow<DrawEditOperation>()
    val undoRedoOperationShared = mUndoRedoOperationShared.asSharedFlow()
    fun SuniaDrawViewController.createOperationWithoutUndoRedo(newDrawEditOperation: DrawEditOperation) = viewModelScope.launch {
        mUndoRedoOperationShared.emit(newDrawEditOperation)
    }


    //强制刷新
    private val _forceUpdatePageIndex = MutableSharedFlow<Int>()
    val forceUpdatePageIndex = _forceUpdatePageIndex.asSharedFlow()

    // 画布内容是否为空
    private val _isContentEmpty = MutableStateFlow(false)
    val isContentEmpty = _isContentEmpty.asStateFlow()

    // 是否要上屏自动保存 for 回忆集内容
    private val _autoSave = MutableStateFlow(false)
    val autoSave = _autoSave.asStateFlow()

    var textListJob: Job? = null
    var haveEdit: Boolean = false
    private val _engineInitComplete = MutableStateFlow(false)
    val engineInitComplete = _engineInitComplete.asStateFlow()
    var isDarkTheme: Boolean = false

    private val _textRectFListState = MutableStateFlow(listOf<RectF>())
    val textRectFListState = _textRectFListState.asStateFlow()


    // 旋转角度
    private val mLassoRotateState = MutableStateFlow<LassoRotateEvent?>(null)
    val lassoRotateState = mLassoRotateState.asStateFlow()

    // 仅修改时触发缩略图保存
    internal var needToSaveThumbnail = false

    // 笔迹美化启用状态
    private val mStrokeBeautifyState = MutableStateFlow(false)
    val strokeBeautifyState  = mStrokeBeautifyState.asStateFlow()

    init {
        updateActualEraserSize()
    }

    fun setEngineInitComplete(complete: Boolean) {
        _engineInitComplete.value = complete
    }

    fun switchStrokeBeautify(enable: Boolean) {
        Logger.v(TAG,"switchStrokeBeautify, enable:$enable")
        mStrokeBeautifyState.update { enable }
    }

    //切换画笔
    fun switchBrush(penProp: PenProp){
        defPenProp.apply {
            penType = penProp.penType
            penColor = penProp.penColor
            penSize = penProp.penSize
            penAlpha = penProp.penAlpha
        }
        changePenProp(defPenProp)
        
        isCurrentlyInEraserMode = false
    }

    private fun changePenProp(penProp: PenProp){
        mPenPropState.update { PenProp(penProp) }
    }

    /**
     * 更新缩放因子
     * 注意拖动也会被认为是缩放，即使缩放倍率没有变化
     */
    fun updateEraseSizeWhenScaleChanged() {
        // 更新实际传递给SDK的橡皮擦大小（只有在橡皮擦模式下才生效）
        updateActualEraserSize()
    }

    private fun updateActualEraserSize() {
        // 注意拖动也会被认为是缩放，即使缩放倍率没有变化
        updateDisplayEraserProp()
        
        val sdkSize = when (displayEraserMode) {
            SuniaEraserMode.LineMode -> 24f // 线擦固定24
            SuniaEraserMode.PointMode -> displayEraserSize
        }
        
        // 只有在橡皮擦模式下才向SDK发送橡皮擦属性更新，兼容按键擦除的场景
        if (isCurrentlyInEraserMode) {
            Logger.d(TAG, "updateActualEraserSize: displaySize=$displayEraserSize, sdkSize=$sdkSize, mode=$displayEraserMode")
            val newDeleteProp = DeleteProp(displayEraserMode, EraserType.CIRCLE, sdkSize, PointF())
            mDeletePropState.update { newDeleteProp }
        }
    }

    /**
     * 模板数据不一样的情况下清空模板文本框位置信息列表
     */
    fun clearTextRectFList() {
        _textRectFListState.update { emptyList() }
    }

    /**
     * 更新橡皮擦属性
     */
    fun switchEraser() {
        isCurrentlyInEraserMode = true
        // 更新显示用的属性（不受缩放影响）
        updateDisplayEraserProp()

        // 更新实际传递给SDK的属性（受缩放影响）
        updateActualEraserSize()
    }
    
    /**
     * 更新橡皮擦属性（不受缩放影响，用于弹框预览和按压指示器）
     */
    private fun updateDisplayEraserProp() {
        val displayEraserProp = DeleteProp(displayEraserMode, EraserType.CIRCLE, displayEraserSize, PointF())
        mDisplayEraserPropState.update { displayEraserProp }
    }

    /**
     * 只更新橡皮擦大小及模式（从本地读取到橡皮擦属性后）
     */
    fun updateEraserData(mode: EraserMode, size: Float){
        val suniaEraserMode = when (mode) {
            EraserMode.AREA -> SuniaEraserMode.PointMode
            EraserMode.STROKE -> SuniaEraserMode.LineMode
        }

        displayEraserMode = suniaEraserMode
        displayEraserSize = size
        Logger.v(TAG,"updateEraserData, displayEraserMode:$displayEraserMode, displayEraserSize:$displayEraserSize")
    }

    /**
     * 切换橡皮擦模式和大小
     */
    fun switchEraser(mode: EraserMode, size: Float) {
        val suniaEraserMode = when (mode) {
            EraserMode.AREA -> SuniaEraserMode.PointMode
            EraserMode.STROKE -> SuniaEraserMode.LineMode
        }
        
        displayEraserMode = suniaEraserMode
        displayEraserSize = size
        
        isCurrentlyInEraserMode = true
        
        Logger.d(TAG, "switchEraser: mode=$mode, size=$size")
        
        // 更新显示用的属性（不受缩放影响）
        updateDisplayEraserProp()
        
        // 更新实际传递给SDK的属性（受缩放影响）
        updateActualEraserSize()
    }

    /**
     * 更改橡皮擦大小
     */
    fun changeEraserSize(size: Float) {
        displayEraserSize = size
        
        // 更新显示用的属性（不受缩放影响）
        updateDisplayEraserProp()
        
        if (isCurrentlyInEraserMode) {
            updateActualEraserSize()
        }
    }

    /**
     * 更改橡皮擦模式
     */
    fun changeEraserMode(mode: EraserMode) {
        val suniaEraserMode = when (mode) {
            EraserMode.AREA -> SuniaEraserMode.PointMode
            EraserMode.STROKE -> SuniaEraserMode.LineMode
        }
        
        displayEraserMode = suniaEraserMode
        
        // 更新显示用的属性（不受缩放影响）
        updateDisplayEraserProp()
        
        if (isCurrentlyInEraserMode) {
            updateActualEraserSize()
        }
    }

    fun ********************(enable: Boolean) {
        mShapeRecognizeState.update { enable }
    }

    fun changePenColor(@ColorInt color: Int, @IntRange(0, 100) alpha: Int ? =null){
        defPenProp.apply {
            penColor =color
            alpha?.let {
                penAlpha = alpha
            }

        }
        changePenProp(defPenProp)
        isCurrentlyInEraserMode = false
    }

    fun clearText() {
        clearTextFuncDelegate.invokeFunc()
    }

    fun changePenSize(size: Float){
        defPenProp.apply {
            penSize = size
        }
        changePenProp(defPenProp)
        isCurrentlyInEraserMode = false
    }

    suspend fun insertBitmap(path: String, rectF: RectF, offset: Int) {
//        viewModelScope.launchIO {
            mInsertBitmapSharedFlow.emit(
                InsertBitmapEvent(path, rectF, offset)
            )
//        }
    }

    fun updateNoteId(noteId: Long?) {
        loadDrawStroke(noteId)
    }

    /**
     * 设计setViewVisibility接口并实现
     *
     * 设置view的可见性
     */
    private val setViewVisibilityFuncDelegate = delegateViewFunc<Int, Unit>()
    // 只有view可见，设置setViewVisibilityFunc属性
    var View.implSetViewVisibilityFunc by setViewVisibilityFuncDelegate
    // 设置view的可见性
    fun setViewVisibility(visibility: Int) {
        setViewVisibilityFuncDelegate.invokeFunc(visibility)
    }

    /**
     * 加载手绘图
     */
    private val loadDrawStrokeFuncDelegate = delegateViewSuspendFunc<Long, Boolean>()
    // 只有view可见，设置loadDrawStrokeFunc属性
    var View.implLoadDrawStrokeFunc by loadDrawStrokeFuncDelegate
    fun loadDrawStroke(noteId: Long?) = viewModelScope.launchIO {
        Logger.v(TAG, "loadDraw, noteId: $noteId")
        mNoteIdState.value = noteId
        // 加载新笔记时重置橡皮擦模式标志
        isCurrentlyInEraserMode = false
        // 发起请求
        noteId?.let { curNoteId ->
            isDrawLoadFinish = false
            val result = loadDrawStrokeFuncDelegate.invokeFunc(noteId)
            Logger.v(TAG, "loadDraw onDataLoaded, noteId: $curNoteId, isSuccess: $result")
            if (<EMAIL> == curNoteId) {
                isDrawLoadFinish = true
            }
        }
    }

    /**
     * 保存手绘图
     */
    private val saveDrawStrokeFuncDelegate = delegateViewFunc<Long, (Boolean) -> Unit, Unit>()
    // 只有view可见，设置saveDrawStrokeFunc属性
    var View.implSaveDrawStrokeFunc by saveDrawStrokeFuncDelegate
    fun saveDraw() = viewModelScope.launchIO {
        Logger.v(TAG, "save: $noteId")
        awaitDrawLoadFinish()
        noteId?.let {
            saveDrawStrokeFuncDelegate.invokeFunc(it) {
                Logger.v(TAG, "saveDraw onDataSaved, noteId: $noteId")
            }
        }
    }

    private val saveThumbnailFuncDelegate = delegateViewFunc<Long, (Boolean) -> Unit, Unit>()
    var View.implSaveThumbnailFunc by saveThumbnailFuncDelegate
    suspend fun saveThumbnail() {
        Logger.v(TAG, "save Thumbnail: $noteId")
        awaitDrawLoadFinish()
        noteId?.let {
            saveThumbnailFuncDelegate.invokeFunc(it) { }
        }
    }

    /**
     * 修改安格斯画布缩放。
     *
     * 绝对缩放值
     */
    private val changeScaleInfoFuncDelegate = delegateViewFunc<MatrixInfo, Unit>()
    var View.implChangeScaleInfoFunc by changeScaleInfoFuncDelegate
    fun changeScaleInfo(matrixInfo: MatrixInfo) {
        Logger.d(TAG, "changeScaleInfo: $matrixInfo")
        changeScaleInfoFuncDelegate.invokeFunc(matrixInfo)
    }

    /**
     * 相对缩放，需要和changeScaleEnd搭配使用
     */
    private val changeScaleRelativeFuncDelegate = delegateViewFunc<MatrixInfo, Unit>()
    var View.implChangeScaleRelativeFunc by changeScaleRelativeFuncDelegate
    fun changeScaleRelative(matrixInfo: MatrixInfo) {
        Logger.d(TAG, "changeScaleRelative: $matrixInfo")
        changeScaleRelativeFuncDelegate.invokeFunc(matrixInfo)
    }

    /**
     * 相对缩放结束
     */
    private val changeScaleEndFuncDelegate = delegateViewFunc<MatrixInfo, Unit>()
    var View.implChangeScaleEndFunc by changeScaleEndFuncDelegate
    fun changeScaleEnd(matrixInfo: MatrixInfo) {
        Logger.d(TAG, "changeScaleEnd")
        changeScaleEndFuncDelegate.invokeFunc(matrixInfo)
    }

    /**
     * 删除所有手绘图
     */
    private val clearAllDrawStrokeFuncDelegate = delegateViewFunc<Unit>()
    var View.implClearAllDrawStrokeFunc by clearAllDrawStrokeFuncDelegate
    fun clearAllDrawStroke() = viewModelScope.launchIO {
        clearAllDrawStrokeFuncDelegate.invokeFunc()
    }

    /**
     * 清空画布
     * */
    private val clearAllFuncDelegate = delegateViewFunc<Unit>()
    var View.implClearAllFunc by clearAllFuncDelegate
    fun clearAll() = viewModelScope.launchIO {
        haveEdit = true
        clearAllFuncDelegate.invokeFunc()
    }

    /**
     * 清除历史栈
     * */
    private val clearStepFuncDelegate = delegateViewFunc<Unit>()
    var View.implClearStepFunc by clearStepFuncDelegate
    fun clearStep() = viewModelScope.launchIO {
        clearStepFuncDelegate.invokeFunc()
    }

    private val handleContentInfoFuncDelegate = delegateViewFunc<JournalContentInfo?, () -> Unit, Unit>()
    var View.handleContentInfoFunc by handleContentInfoFuncDelegate
    fun handleContentInfo(journalContentInfo: JournalContentInfo?, onComplete: () -> Unit) = viewModelScope.launchIO {
        handleContentInfoFuncDelegate.invokeFunc(journalContentInfo, onComplete)
    }

    private val replaceTextFuncDelegate = delegateViewFunc<List<String>, JournalContentInfo?, Unit>()
    var View.implReplaceTextFunc by replaceTextFuncDelegate
    fun replaceText(textList: List<String>, journalContentInfo: JournalContentInfo?) = viewModelScope.launchIO {
        replaceTextFuncDelegate.invokeFunc(textList, journalContentInfo)
    }

    private val addTextFuncDelegate = delegateViewFunc<String, Unit>()
    var View.implAddTextFunc by addTextFuncDelegate
    fun addText(text: String) = viewModelScope.launchIO {
        // 这里可以添加逻辑来处理文本添加
        addTextFuncDelegate.invokeFunc(text)
    }
    /**
     * 撤销
     */
    // undo/redo功能
    private val undoFuncDelegate = delegateViewFunc<Unit>()
    var View.implUndoFunc by undoFuncDelegate
    fun undo() {
        haveEdit = true
        undoFuncDelegate.invokeFunc()
    }

    /**
     * 操作
     */
    private val redoFuncDelegate = delegateViewFunc<Unit>()
    var View.implRedoFunc by redoFuncDelegate

    /**
     * 清除文本
     */
    private val clearTextFuncDelegate = delegateViewFunc<Unit>()
    var View.clearTextFunc by clearTextFuncDelegate

    fun redo() {
        haveEdit = true
        redoFuncDelegate.invokeFunc()
    }

    /**
     * 获取画板有效内容范围
     */
    private val getScaledContentRangeDelegate = delegateViewSuspendFunc<RectF>()
    var View.implGetScaledContentRangeFunc by getScaledContentRangeDelegate
    suspend fun getScaledContentRange() = coroutineScope {
        awaitDrawLoadFinish()
        getScaledContentRangeDelegate.invokeFunc()
    }

    private val isContentEmptyDelegate = delegateViewSuspendFunc<Boolean>()
    var View.implIsContentEmptyFunc by isContentEmptyDelegate
    suspend fun isContentEmpty() = coroutineScope {
        isContentEmptyDelegate.invokeFunc()
    }

    private val getCurveCountFuncDelegate = delegateViewSuspendFunc<Int>()
    var View.implGetCurveCountFunc by getCurveCountFuncDelegate
    suspend fun getCurveCount() = coroutineScope {
        getCurveCountFuncDelegate.invokeFunc()
    }

    private val getImageCountFuncDelegate = delegateViewSuspendFunc<Int>()
    var View.implGetImageCountFunc by getImageCountFuncDelegate
    suspend fun getImageCount() = coroutineScope {
        getImageCountFuncDelegate.invokeFunc()
    }

    /**
     * 将画板内容绘制到bitmap
     */
    private val toBitmapDelegate = delegateViewSuspendFunc<Bitmap, Bitmap>()
    var View.implToBitmapFunc by toBitmapDelegate
    suspend fun toBitmap(bitmap: Bitmap) = coroutineScope {
        awaitDrawLoadFinish()
        toBitmapDelegate.invokeFunc(bitmap)
    }

    /**
     * 删除选择内容
     */
    private val deleteSelectFuncDelegate = delegateViewFunc<Unit>()
    var View.implDeleteEditSelectFunc by deleteSelectFuncDelegate
    fun deleteEditSelect() {
        deleteSelectFuncDelegate.invokeFunc()
    }

    /**
     * 取消框选
     */
    private val finishSelectFuncDelegate = delegateViewFunc<Unit>()
    var View.implFinishSelectFunc by finishSelectFuncDelegate
    fun finishSelect() {
        finishSelectFuncDelegate.invokeFunc()
    }


    // 更新undo/redo状态
    fun updateUndoRedoState(canUndo: Boolean, canRedo: Boolean) {
        _canUndoState.value = canUndo
        _canRedoState.value = canRedo
    }

    // 更新当前触摸位置（用于橡皮擦指示器）
    fun updateCurrentTouchPosition(position: androidx.compose.ui.geometry.Offset?) {
        _currentTouchPositionState.value = position
    }

    fun setImageCount(count: Int) {
        mImageCountState.value = count
    }

    fun setShowRect(left: Int, top: Int, right: Int, bottom: Int) {
        mShowRectState.update {
            RectF(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat())
        }
    }

    /*private fun emitEvent(event: DrawStrokeEvent) = viewModelScope.launch {
        mEventFlow.emit(event)
    }*/

    private val saveJournalDelegate = delegateViewFunc<Long, Int, Unit>()
    var View.saveJournalFunc by saveJournalDelegate
    fun saveJournalContent(journalId: Long, pageIndex: Int) {
        saveJournalDelegate.invokeFunc(journalId, pageIndex)
    }

    open fun autoSaveJournalContent(){}

    fun isContentEmpty(isContentEmpty: Boolean) {
        _isContentEmpty.update { isContentEmpty }
    }

    fun forceUpdatePageIndex(pageIndex: Int) {
        viewModelScope.launchIO {
            Logger.d(TAG, "forceUpdatePageIndex pageIndex: $pageIndex")
            _forceUpdatePageIndex.emit(pageIndex)
        }
    }

    fun setAutoSave(autoSave: Boolean) {
        _autoSave.update { autoSave }
    }

    fun setTextRectFList(textRectFList: List<RectF>) {
        _textRectFListState.update { textRectFList }
    }

    fun setLassoEnable(enable: Boolean) {
        mLassoState.update { enable }
    }

    fun updateEditState(isEdit: Boolean, x: Float = 0F, y: Float = 0F) {
        mLassoEditState.value = LassoEditEvent(isEdit, x, y)
    }

    fun updateRotateState(rotate: LassoRotateEvent?) {
        mLassoRotateState.value = rotate
    }

    fun setInsertImageScrollDistance(insertBitmapEvent: InsertBitmapEvent?) {
        mInsertImageScrollDistanceState.update { insertBitmapEvent }
    }

    fun longPressPoint(pointF: PointF) {
        viewModelScope.launchIO {
            _onClickPointState.emit(
                pointF
            )
        }
    }

    companion object {
        private const val TAG = "SuniaDrawViewModel"
        private const val SP_KEY_FINGER_DRAW_ENABLED = "sp_key_finger_draw_enabled"
    }
}

data class InsertBitmapEvent(
    val path: String,
    val rectF: RectF,
    val offset: Int = 0
)

data class LassoEditEvent(
    val isEdit: Boolean,
    val x: Float = 0f,
    val y: Float = 0f
)

data class LassoRotateEvent(
    val rotate: String,
    val x: Float = 0f,
    val y: Float = 0f
)
