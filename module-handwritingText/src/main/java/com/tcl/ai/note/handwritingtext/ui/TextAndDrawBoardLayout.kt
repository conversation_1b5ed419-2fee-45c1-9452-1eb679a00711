package com.tcl.ai.note.handwritingtext.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.graphics.PointF
import android.graphics.RectF
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import android.view.MotionEvent.BUTTON_STYLUS_PRIMARY
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.Toast
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.UndoRedoType
import com.tcl.ai.note.handwritingtext.richtext.history.UndoStackOp
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.sunia.SuniaDrawViewController
import com.tcl.ai.note.handwritingtext.ui.richtext.RichTextController
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.ui.utils.scale.OffsetAndScaleHandler
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.AccessibilityUtils
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.isLandScapeByScreenSize
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.launchMain
import com.tcl.ai.note.utils.screenSizeMin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import okhttp3.internal.toHexString
import java.util.LinkedList
import kotlin.math.absoluteValue
import kotlin.math.max

@SuppressLint("ViewConstructor")
open class TextAndDrawBoardLayout(
    private val context: Context,
    private val richTextViewModel: RichTextViewModel2,
    private val suniaDrawViewModel: SuniaDrawViewModel,
    private val textAndDrawViewModel: TextAndDrawViewModel,
    private val menuBarViewModel: MenuBarViewModel,
    private var needToLoadInFirstEnter: Boolean = true,
) : FrameLayout(context) {
    protected val richTextController =
        RichTextController(context, richTextViewModel)
    protected val suniaDrawViewController =
        SuniaDrawViewController(context, suniaDrawViewModel)

    protected val currentScaleInfo get() = textAndDrawViewModel.matrixInfo

    private var isDestroyed = false
    private var isFirstEnterLoading = true

    private var lastLoadingTime = System.currentTimeMillis()

    // 统一处理富文本和手绘的UndoRedo操作
    protected val globalUndoRedoHandler = GlobalUndoRedoHandler()
    
    // 底部工具栏高度
    private var richTextBottomPadding = 0
    // 富文本永远比手绘要高，因为富文本包含手绘
    private var richTextOrDrawBroadMaxHeight = 0

    // 接管缩放位移处理
    protected val offsetAndScaleHandler = OffsetAndScaleHandler(
        defaultMatrixInfo(),
        ::onScrollAndScaleStart,
        ::onScrollingAndScaling,
        ::onScrollAndScaleEnd,
    )

    private var gestureDetector: GestureDetector? = null

    private var cursorToScreenScrollEndJob: Job? = null

    private fun implViewModelFunc() = with(textAndDrawViewModel) {
        implUndoFunc = globalUndoRedoHandler::undo
        implRedoFunc = globalUndoRedoHandler::redo
        implChangeScaleFunc = offsetAndScaleHandler::updateScaleInfo
    }

    init {
        implViewModelFunc()
        layoutParams = LayoutParams(
            LayoutParams.MATCH_PARENT,
            LayoutParams.MATCH_PARENT,
        )
        visibility = View.INVISIBLE
        // 添加富文本和手绘两个子view
        attachViewGroup(this)
        // 禁止水平滚动
        offsetAndScaleHandler.setOffsetLimit(minOffsetX = { 0f }, maxOffsetX = { 0f })

        // 监听光标拖动柄移动布局
        richTextController.setScrollByCursorHandleListener(
            onScrollByCursorHandleStart = { translationX: Float, translationY: Float ->
                val oldScaleInfo = offsetAndScaleHandler.matrixInfo
                val newScaleInfo = MatrixInfo(
                    translationX,
                    translationY,
                    oldScaleInfo.scale
                )
                offsetAndScaleHandler.doScale(newScaleInfo)
            },
            onScrollByCursorHandleEnd = { translationX: Float, translationY: Float ->
                val oldScaleInfo = offsetAndScaleHandler.matrixInfo
                val newScaleInfo = MatrixInfo(
                    translationX,
                    translationY,
                    oldScaleInfo.scale
                )
                offsetAndScaleHandler.doScaleEnd(newScaleInfo)
            },
        )

        // 键盘输入时，或者键盘弹出时。光标要回到屏幕内
        richTextController.setOnCursorToScreenListener { translationX: Float, translationY: Float ->
            // 转换成scaleInfo
            val oldScaleInfo = offsetAndScaleHandler.matrixInfo
            if (oldScaleInfo.offsetX != translationX || oldScaleInfo.offsetY != translationY) {
                val newScaleInfo = MatrixInfo(
                    translationX,
                    translationY,
                    oldScaleInfo.scale
                )
                Logger.d(TAG, "setOnCursorToScreenListener: $newScaleInfo")
                // 监听光标回到屏幕的位移变化
                offsetAndScaleHandler.doScale(newScaleInfo)
                // 延迟触发end，不确定是否有bug
                cursorToScreenScrollEndJob?.cancel()
                cursorToScreenScrollEndJob = textAndDrawViewModel.viewModelScope.launch {
                    delay(200)
                    offsetAndScaleHandler.doScaleEnd(newScaleInfo)
                }
            }
        }

        richTextController.onLimitToast = {
            Toast.makeText(context, com.tcl.ai.note.base.R.string.max_content_length, Toast.LENGTH_SHORT).show()
        }

        // 设置预览模式下点击切换到编辑模式的回调
        richTextController.onPreviewModeClickToEdit = {
            // 从预览模式切换到文本编辑模式
            textAndDrawViewModel.editMode = EditMode.TEXT
        }

        // 监听手绘大小变化和富文本大小变化
        textAndDrawViewModel.viewModelScope.launchIO {
            combine(
                suniaDrawViewModel.drawStokeVisibleRectState,
                richTextViewModel.richTextSizeState,

            ) { drawRect, richTextLayout ->
                Pair(drawRect, richTextLayout)
            }.collectLatest { (drawRect, richTextLayoutSize) ->
                suniaDrawViewModel.awaitDrawLoadFinish()
                val drawStrokeHeight = drawRect.bottom.toInt()
                // 取父View长边，作为填充的大小
                val longEdge = max(measuredWidth, measuredHeight)
                Logger.d(TAG, "changeRichTextLayoutHeight, drawStrokeHeight: $drawStrokeHeight, richTextLayout.height: ${richTextLayoutSize.height}, longEdge: $longEdge")
                if (richTextLayoutSize.height != 0 && longEdge != 0) {
                    val maxHeight = maxOf(drawStrokeHeight + longEdge, richTextLayoutSize.height, longEdge * 2)
                    // 富文本高度默认为View的两倍高
                    // 富文本高度始终比手绘高度大一个longEdge
                    if (maxHeight > richTextLayoutSize.height) {
                        delay(100)
                        Logger.i(TAG, "changeRichTextLayoutHeight, maxHeight: $maxHeight")
                        richTextController.updateLayoutParams { layoutParams ->
                            layoutParams.height = maxHeight
                            layoutParams
                        }
                        // 添加换行
                        richTextViewModel.addLineBreaksAtEnd(2)
                    }
                    richTextOrDrawBroadMaxHeight = maxHeight
                    applyMaxScrollDistance()
                }
            }
        }

        suniaDrawViewController.onSetVisibleSizeFinish { w: Int, h: Int, oldw, oldh ->
            Logger.d(TAG, "setVisibleSizeFinish")
            var newMatrixInfo = currentScaleInfo
            if (isLandScapeByScreenSize) {
                if ((currentScaleInfo.scale * 100).toInt() == 100) {
                    newMatrixInfo = currentScaleInfo.copy(scale = 1.2f)
                }
            } else {
                if ((currentScaleInfo.scale * 100).toInt() == 120) {
                    newMatrixInfo = currentScaleInfo.copy(scale = 1f)
                }
            }
            if (w != oldw) {
                offsetAndScaleHandler.updateScaleInfo(newMatrixInfo)
            }
        }

        suniaDrawViewController.onCanvasHandleFinish {
            textAndDrawViewModel.onLoading.update { false }
            textAndDrawViewModel.viewModelScope.launchMain {
                if (visibility != View.VISIBLE) {
                    visibility = View.VISIBLE
                }
            }
            val take = System.currentTimeMillis() - lastLoadingTime
            Logger.v(TAG, "onCanvasHandleFinish, loading take: $take ms")
        }
        suniaDrawViewController.onSizeChangeStart { w: Int, h: Int, oldw: Int, oldh: Int ->
            lastLoadingTime = System.currentTimeMillis()
//            textAndDrawViewModel.viewModelScope.launchMain {
//                if (visibility != View.INVISIBLE) {
//                    visibility = View.INVISIBLE
//                }
//            }
            if (isFirstEnterLoading && !needToLoadInFirstEnter) {
                Logger.d(TAG, "first enter loading and no need to load, skip to show loading")
                isFirstEnterLoading = false
                return@onSizeChangeStart
            }
            if (w != oldw) {
                textAndDrawViewModel.onLoading.update { true }
            }
        }

        // 每次手绘都会回调
        suniaDrawViewController.onLastDrawRect {
            // 富文本光标自动定位到最新笔画的位置
            Logger.i(TAG, "onLastDrawRect: $it")
            richTextViewModel.viewModelScope.launch(Dispatchers.Main.immediate) {
                // 第一次绘制，更新一下Note数据使之更改为可见状态
                if(!richTextViewModel.uiState.value.isShow){
                    richTextViewModel.uiState.value.isShow = true
                    richTextViewModel.saveNote()
                }

                val point = PointF(it.left, it.bottom)

                richTextController.addNewLineByPoint(point)
                richTextController.setCursorIndexByPoint(point)
            }
//            richTextController.lastDrawRect = it
        }
        suniaDrawViewController.onTextModeSwitchDrawMode { hasImage ->
            Logger.e(TAG, "onPressPointHasImage: $hasImage, ${textAndDrawViewModel.editMode}")
            if (hasImage) {
                // 切换到手绘模式
                if (textAndDrawViewModel.editMode != EditMode.DRAW) {
                    menuBarViewModel.switchToDrawingMode()
                    textAndDrawViewModel.editMode = EditMode.DRAW
                    suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
                }
            } else {
            }
        }

        textAndDrawViewModel.viewModelScope.launchMain {
            textAndDrawViewModel.editModeState.collectLatest { editMode ->
                Logger.e(TAG, "onPressPointHasImage: editMode=$editMode")
                when (editMode) {
                    is EditMode.LOADING -> {
                        richTextController.changeEditable(false)
                        suniaDrawViewController.changeEditable(false)
                    }

                    EditMode.TEXT -> {
                        suniaDrawViewController.changeEditable(false)
                        richTextController.changeEditable(true)
                    }

                    EditMode.DRAW -> {
                        richTextController.changeEditable(false)
                        suniaDrawViewController.changeEditable(true)
                    }

                    EditMode.PREVIEW -> {
                        // 预览模式下允许富文本接收点击事件，但不主动请求焦点
                        suniaDrawViewController.changeEditable(false)
                        richTextController.changeEditableForPreview(true)
                    }
                }
            }
        }
        gestureDetector = GestureDetector(context, object : SimpleOnGestureListener() {
            override fun onLongPress(e: MotionEvent) {
                super.onLongPress(e)
                Logger.d(TAG, "onLongPress")
                // 去掉长按选择图片功能
                /*if (textAndDrawViewModel.editMode != EditMode.DRAW) {
                    // 长按时，调用sunia接口判断是否有图片，进入图片编辑模式
                    suniaDrawViewModel.longPressPoint(PointF(e.x, e.y))
                }*/
            }

            override fun onSingleTapUp(e: MotionEvent): Boolean {
                Logger.d(TAG, "onSingleTapUp")
                if (textAndDrawViewModel.editMode != EditMode.DRAW /*|| isAutoSwitchToDrawMode*/) {
                    Logger.d(TAG, "onSingleTapUp longPressPoint")
                    suniaDrawViewModel.longPressPoint(PointF(e.x, e.y))
                }
                return super.onSingleTapUp(e)
            }
        })
    }

    /**
     * 处理缩放开始逻辑
     */
    protected open fun defaultMatrixInfo(): MatrixInfo {
        return textAndDrawViewModel.matrixInfo
    }

    /**
     * 处理缩放开始逻辑
     */
    protected open fun onScrollAndScaleStart(matrixInfo: MatrixInfo, overRect: RectF, isGesture: Boolean) {
        isAlreadyShowLimitScrollToast = false
        textAndDrawViewModel.matrixInfo = matrixInfo
        Logger.d(TAG, "onScrollAndScaleStart")
    }

    /**
     * 处理正在缩放逻辑
     */
    protected open fun onScrollingAndScaling(matrixInfo: MatrixInfo, overRect: RectF, isGesture: Boolean) {
        // 监听手势的缩放变化，并传递给子view
        textAndDrawViewModel.matrixInfo = matrixInfo

        // 手绘是安格斯的，无法直接修改。只能在横屏的时候乘以系数。达到缩小效果
        suniaDrawViewModel.changeScaleRelative(matrixInfo)
        richTextViewModel.changeScaleInfo(matrixInfo)

        // overRect.bottom == 0代表已经滚动到底部
        // 只有手势滑动才触发
        showToastWhenScrollToBottom(overRect, isGesture)
        Logger.d(TAG, "onScrollAndScale, matrixInfo@${System.identityHashCode(matrixInfo).toHexString()}: $matrixInfo")
    }

    /**
     * 处理缩放结束逻辑
     */
    protected open fun onScrollAndScaleEnd(matrixInfo: MatrixInfo, overRect: RectF, isGesture: Boolean) {
        Logger.d(TAG, "onScrollAndScaleEnd")
        textAndDrawViewModel.matrixInfo = matrixInfo
        suniaDrawViewModel.changeScaleEnd(matrixInfo)

        if (matrixInfo.scale < 1f) {
            val left = left + (width - screenSizeMin * matrixInfo.scale) / 2
            suniaDrawViewModel.setShowRect(
                left.toInt(),
                top,
                (left + screenSizeMin* matrixInfo.scale).toInt(),
                bottom
            )
        } else {
            val left = left + (width - screenSizeMin) / 2
            suniaDrawViewModel.setShowRect(
                left,
                top,
                left + screenSizeMin,
                bottom
            )
        }

    }

    /**
     * 手势滚动到底部时，弹出toast
     */
    // 本轮手势缩放是否已经弹过toast事件
    private var isAlreadyShowLimitScrollToast = false
    protected open fun showToastWhenScrollToBottom(overRect: RectF, isGesture: Boolean) {
        if (isGesture && isAlreadyShowLimitScrollToast == false && overRect.bottom >= 0) {
            isAlreadyShowLimitScrollToast = true
            ToastUtils.makeWithCancel(R.string.no_more_content)
        }
    }

    /**
     * 重新设置滚动距离
     */
    protected open fun applyMaxScrollDistance() {
        // 滚动距离就是富文本的距离，但需要加上富文本工具栏和减去父view的高度。
        // 重新设置滚动距离
        var maxScrollDistance = richTextOrDrawBroadMaxHeight - measuredHeight
        if (maxScrollDistance > 0) {
            // 限制上滑距离, 上滑取负数
            offsetAndScaleHandler.setOffsetLimit(
                // richTextBottomPadding不需要乘以缩放因子, 所以richTextBottomPadding要除以scale
                minOffsetY = { scale -> -maxScrollDistance.toFloat() - richTextBottomPadding / scale },
                maxOffsetY = { 0f },
            )
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        val left = left + (width - screenSizeMin) / 2

        // 把可视范围设置给offsetAndScaleHandler，用于计算边界，并返回最新的缩放信息
        textAndDrawViewModel.matrixInfo = offsetAndScaleHandler.setVisibleSizeAndGetMatrixInfo(
            left,
            top,
            left + screenSizeMin,
            bottom
        )
        // 布局变化，重置最大滚动量
        applyMaxScrollDistance()

        Logger.d(TAG, "TextAndDrawBoardLayout onLayout: $measuredWidth, $measuredHeight, left: $left, top: $top, right: ${left + screenSizeMin}, bottom: $bottom")
    }

    /**
     * 处理事件
     */
    // 大于dragDistanceThreshold，则认为被拖拽，而不是点击
    private val dragDistanceThreshold = ViewConfiguration.get(context).scaledTouchSlop
    // 是否被拖拽
    private var isRichTextDragged = false
    // down事件的x,y
    private var startX = 0f
    private var startY = 0f

    // 标记是否需要补点操作（擦除模式下 down时在画布内，移动到画布外，再次移回画布内时需要手动补点down事件）
    private var isReplenishPoint = false
    private var touchableRect = RectF()
    private var validRect = RectF()

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (ev == null) return super.dispatchTouchEvent(ev)
        gestureDetector?.onTouchEvent(ev)

        if (ev.actionMasked == MotionEvent.ACTION_DOWN) {
            passiveTriggerErase(ev)
            startX = ev.x
            startY = ev.y
            isRichTextDragged = false
            touchableRect = getViewRealRectInParent()
            val scaleFactor = screenSizeMin.toFloat() / measuredWidth.toFloat()
            validRect = getScaledAndCenteredRect(touchableRect,currentScaleInfo.scale * scaleFactor)
            Logger.d(TAG,"dispatchTouchEvent, ACTION_DOWN, scaleFactor:$scaleFactor,scale * scaleFactor:${currentScaleInfo.scale * scaleFactor},validRect:$validRect")

        }

        val isValidTouchEvent = validRect.contains(ev.x, ev.y)
        val isEraserActive = menuBarViewModel.menuBarState.value.isEraserActive
                || menuBarViewModel.menuBarState.value.isPassiveEraserActive
        Logger.d(TAG, "dispatchTouchEvent, isEraserActive:$isEraserActive, isValidTouchEvent:  $isValidTouchEvent, ev:$ev")

        // 拦截事件影响到了缩放的场景，导致擦除或书写卡顿，需要兼容：如果是多指或防误触，则不拦截
        if (isEraserActive && !isValidTouchEvent && ev.pointerCount == 1) {
            // 擦除模式下移动到屏幕外手动补up事件
            Logger.d(TAG,"dispatchTouchEvent, Move outside canvas")
            val cancelEvent = MotionEvent.obtain(ev).apply { action = MotionEvent.ACTION_UP }
            val isInterrupted = super.onTouchEvent(cancelEvent)
            isReplenishPoint = true
            suniaDrawViewController.dispatchTouchEvent(cancelEvent)
            offsetAndScaleHandler.detectGesture(cancelEvent)
            switchBackToBrush(cancelEvent,true)
            cancelEvent.recycle()
            return isInterrupted
        }else if (isReplenishPoint && isEraserActive && isValidTouchEvent){
            Logger.d(TAG,"dispatchTouchEvent, Move into canvas")
            // 擦除模式下 down时在画布内，移动到画布外，再次移回画布内时需要手动补点down事件
            val downEvent = MotionEvent.obtain(ev).apply { action = MotionEvent.ACTION_DOWN }
            passiveTriggerErase(downEvent)
            suniaDrawViewController.dispatchTouchEvent(downEvent)
            isReplenishPoint = false
        }

        if (ev.actionMasked == MotionEvent.ACTION_UP || ev.actionMasked == MotionEvent.ACTION_CANCEL) {
            switchBackToBrush(ev,false)
        }

        // 双指，分发缩放位移事件
        if (ev.pointerCount >= 2) {
            if (!isRichTextDragged) {
                isRichTextDragged = true
                // 避免触发富文本的长按逻辑
                val cancelEvent = MotionEvent.obtain(ev).apply {
                    action = MotionEvent.ACTION_CANCEL
                }
                richTextController.dispatchTouchEvent(cancelEvent)
                suniaDrawViewController.dispatchTouchEvent(cancelEvent)
                cancelEvent.recycle()
            }
            offsetAndScaleHandler.detectGesture(ev)
            return true
        }

        // 单指，防误触功能启动（关闭手指绘制笔迹enableFingerDrawing==false）
        // 需要把手指绘制笔迹操作改成手指位移画布
        // 取消时类型可能是TOOL_TYPE_UNKNOWN，增加判断
        if (textAndDrawViewModel.editMode == EditMode.DRAW
            && !suniaDrawViewModel.enableFingerDrawingState.value
            && ev.pointerCount == 1
            && (ev.getToolType(0) == MotionEvent.TOOL_TYPE_FINGER
                    || ev.getToolType(0) == MotionEvent.TOOL_TYPE_UNKNOWN)
        ) {
            // 取消框选
            suniaDrawViewModel.finishSelect()
            offsetAndScaleHandler.detectGesture(ev)
            return true
        }

        // 单指，而且在手绘状态，需要发送ACTION_UP判断缩放结束。
        // 否则在手绘状态下onScrollAndScaleEnd无法回调
        if (isRichTextDragged
            && (ev.actionMasked == MotionEvent.ACTION_UP || ev.actionMasked == MotionEvent.ACTION_CANCEL)
        ) {
            Logger.d(TAG, "dispatchTouchEvent gesture end: $ev")
            offsetAndScaleHandler.detectGesture(ev)
        }

        // 专门处理富文本单指位移
        if (textAndDrawViewModel.editMode == EditMode.TEXT
            && ev.pointerCount == 1
        ) {
            // 处理单指位移
            when (ev.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    richTextController.dispatchTouchEvent(ev)
                    offsetAndScaleHandler.detectGesture(ev)
                    Logger.d(TAG, "dispatchTouchEvent, EditMode.TEXT: $ev")
                    return true
                }

                else -> {
                    // 获取偏移距离
                    val offsetX = ev.x - startX
                    val offsetY = ev.y - startY
                    val distance = max(offsetX, offsetY).absoluteValue
                    if (distance < dragDistanceThreshold && !isRichTextDragged) {
                        // 富文本没有被拖动，分发事件
                        richTextController.dispatchTouchEvent(ev)
                        Logger.d(TAG, "dispatchTouchEvent, EditMode.TEXT: $ev")
                    } else {
                        if (!isRichTextDragged) {
                            // 本轮触摸事件被判断为富文本拖动，不再给富文本分发事件。交给位移缩放处理
                            isRichTextDragged = true
                            // 富文本被拖动了，发送取消事件
                            val cancelEvent = MotionEvent.obtain(ev).apply {
                                action = MotionEvent.ACTION_CANCEL
                            }
                            richTextController.dispatchTouchEvent(cancelEvent)
                            Logger.d(TAG, "dispatchTouchEvent, EditMode.TEXT: $cancelEvent")
                            cancelEvent.recycle()
                        }
                        offsetAndScaleHandler.detectGesture(ev)
                    }
                    return true
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 计算缩放后水平居中显示的矩形区域（仅宽度缩放，高度不变）
     * @param originalRect 原始矩形区域（通常等于屏幕尺寸）
     * @param scale 缩放比例（仅应用于宽度方向）
     * @return 缩放后水平居中且高度不变的矩形区域
     */
    private fun getScaledAndCenteredRect(
        originalRect: RectF,
        scale: Float,
    ): RectF {
        // 计算缩放后宽度
        val scaledWidth = originalRect.width() * scale

        // 高度保持不变
        val resultRect = RectF().apply {
            top = originalRect.top
            bottom = originalRect.bottom
            left = originalRect.centerX() - scaledWidth / 2
            right = left + scaledWidth
        }

        // 不超出屏幕左右边界
        if (resultRect.left < originalRect.left) {
            resultRect.offset(originalRect.left - resultRect.left, 0f)
        }
        if (resultRect.right > originalRect.right) {
            resultRect.offset(originalRect.right - resultRect.right, 0f)
        }
        return resultRect
    }

    /**
     * 获取缩放位移后，view的实际位置
     */
    private fun getViewRealRectInParent(): RectF {
        return RectF(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat())
    }

    /**
     * 新增 按键擦除功能
     * 手写笔按下按键时buttonState != 0,此时 需要将书写模式转为擦除
     * 系统会补上一笔的up和下一笔的down事件，事件是完整的
     */
    private fun passiveTriggerErase(event: MotionEvent){
        val time = System.currentTimeMillis()
        val isStylusButtonPressed =  isStylusButtonPressed(event)
        val menuBarState = menuBarViewModel.menuBarState.value
        val isPassiveEraserActive = menuBarState.isPassiveEraserActive
        val isEraserActive = menuBarState.isEraserActive
        val isBeautifyActive = menuBarState.isBeautifyActive
        val isBrushActive = menuBarState.isBrushActive

        Logger.v(TAG,"passiveTriggerErase, isStylusButtonPressed:$isStylusButtonPressed, isBrushActive:$isBrushActive, " +
                "isBeautifyActive:$isBeautifyActive, isPassiveEraserActive:$isPassiveEraserActive, isEraserActive:$isEraserActive, ev:$event")
        if (!isPassiveEraserActive && (isBrushActive || isBeautifyActive ) && isStylusButtonPressed){//当前为非擦除模式，处于擦除模式则不用再切了
            //临时切换至擦除模式
            menuBarViewModel.switchToTempEraserMode(isBrushActive,isBeautifyActive)
            Logger.v(TAG,"passiveTriggerErase, press button switch to erase, deletePropState.value:${suniaDrawViewModel.deletePropState.value}")
            suniaDrawViewModel.switchEraser()
        } else if (isPassiveEraserActive && !isEraserActive && !isStylusButtonPressed){
            //如果之前是按键擦除切到擦除，则松下按键时，需要切会书写
            Logger.v(TAG,"passiveTriggerErase, panasonic button, switch back to write")
            switchBrush(isBeautifyActive)
        }
        Logger.v(TAG,"passiveTriggerErase, use time:${System.currentTimeMillis() - time}")
    }

    /**
     * up时 如果之前是按键触发的被动擦除，则需要主动切回书写模式
     * 不管有没有松手笔上的按键，按键触发的模式切换，up后均需要切回书写模式
     */
    private fun switchBackToBrush(event: MotionEvent, isExtraPoint: Boolean){
        val time = System.currentTimeMillis()
        val isStylusButtonPressed =  isStylusButtonPressed(event)
        val menuBarState = menuBarViewModel.menuBarState.value
        val isEraserActive = menuBarState.isEraserActive
        val isPassiveEraserActive = menuBarState.isPassiveEraserActive
        val isBeautifyActive =  menuBarState.isBeautifyActive
        Logger.v(TAG,"action up, isExtraPoint:$isExtraPoint, isStylusButtonPressed:${isStylusButtonPressed}, isEraserActive:$isEraserActive," +
                "isBeautifyActive:$isBeautifyActive, isPassiveEraserActive:$isPassiveEraserActive")
        if (isPassiveEraserActive && !isEraserActive && !isExtraPoint){
            //如果之前是按键擦除切到擦除，则松下按键时，需要切会书写
            //美化笔适配按键擦除
            Logger.v(TAG,"action up, press the Panasonic button and switch back to write")
            switchBrush(isBeautifyActive)
        }
        Logger.v("TAG","action up, use time:${System.currentTimeMillis() - time}")
    }


    private fun switchBrush(isBeautifyActive: Boolean){
        Logger.e(TAG,"switchBrush, isBeautifyActive:$isBeautifyActive")
        if (isBeautifyActive){
            menuBarViewModel.onBeautifyClick()
        }else{
            menuBarViewModel.switchToDrawingMode()
        }
        textAndDrawViewModel.editMode = EditMode.DRAW
        suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
    }

    // 添加子view
    protected open fun attachViewGroup(viewGroup: ViewGroup) {
        val emptyView = View(context).apply {
            layoutParams = LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT
            )
            // TODO: talkback下，手指或笔点击都会先变成hover事件，导致焦点无法移动到富文本
            setOnHoverListener { _, _ ->
                // 在文本模式且开启talkback时，不拦截hover
                textAndDrawViewModel.editMode != EditMode.TEXT || !AccessibilityUtils.isExploreByTouchEnabled
            }
        }
        richTextController.attachViewGroup(viewGroup)
        // 中间层，拦截富文本hover事件，避免笔hover时出现光标
        viewGroup.addView(emptyView)
        suniaDrawViewController.attachViewGroup(viewGroup)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        Logger.v(TAG, "onSizeChanged, w: $w, h: $h")
        super.onSizeChanged(w, h, oldw, oldh)
//        if (visibility == View.VISIBLE) {
//            visibility = View.INVISIBLE
//        }
//        offsetAndScaleHandler.setScaleLimit(
//            SCALE_UNIFIED_MIN * scaleFactor,
//            SCALE_UNIFIED_MAX * scaleFactor
//        )
    }

    fun setupRichTextBottomPadding(bottomMargin: Int, bottomMenuBarHeight: Int = 0) {
        richTextBottomPadding = bottomMargin + bottomMenuBarHeight
        richTextController.updateBottomMargin(bottom = bottomMargin)
        richTextController.updateBottomMenuBarHeight(bottomMenuBarHeight)
        applyMaxScrollDistance()
    }

    fun changeDarkTheme(isDark: Boolean) {
        suniaDrawViewController.changeDarkTheme(isDark)
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        // 分屏拖动时，AREditText会销毁重建，需要重新设置文本内容
//        RichTextEventManager.triggerTextOperateEvent(RichTextOperateEvent.LoadNote(richTextViewModel.uiState.value.content,richTextViewModel.uiState.value.richTextStyleEntity))
    }

    /**
     * 销毁布局，清理所有资源
     */
    protected open fun destroy() {
        cursorToScreenScrollEndJob?.cancel()
        if (isDestroyed) return
        isDestroyed = true

        Logger.d(TAG, "Destroying TextAndDrawBoardLayout")

        // 清理控制器
        richTextController.destroy()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        destroy()
    }

    /**
     * 判断用户是否按下手写笔按键
     */
    private fun isStylusButtonPressed(event: MotionEvent): Boolean {
        return (event.buttonState and BUTTON_STYLUS_PRIMARY) != 0
    }

    /**
     * 检查键盘是否处于活跃状态
     */
    fun isKeyboardActive(): Boolean {
        return richTextController.isKeyboardActive()
    }

    /**
     * 检查富文本是否有焦点
     */
    fun hasRichTextFocus(): Boolean {
        return richTextController.hasFocus()
    }

    /**
     * 获取当前光标位置
     */
    fun getRichTextCursorPosition(): Int {
        return richTextController.getCurrentCursorPosition()
    }

    /**
     * 恢复编辑状态
     */
    fun restoreEditState(cursorPosition: Int, shouldShowKeyboard: Boolean) {
        richTextController.restoreEditState(cursorPosition, shouldShowKeyboard)
    }

    companion object {
        const val TAG = "TextAndDrawBoard"
        private const val SCALE_UNIFIED_MAX = 6f
        private const val SCALE_UNIFIED_MIN = 0.6f
    }

    /**
     * 统一处理富文本和手绘的undo redo操作
     */
    inner class GlobalUndoRedoHandler {
        // 撤销栈
        private val mUndoStack = LinkedList<UndoRedoType>()
        // 重做栈
        private val mRedoStack = LinkedList<UndoRedoType>()

        init {
            // 记录手绘或者富文本的undo栈改动
            textAndDrawViewModel.viewModelScope.launchMain {
                launch {
                    richTextViewModel.undoStackChangedShared.collect { (editOperation, op) ->
                        when (op) {
                            UndoStackOp.Add -> textAndDrawViewModel.run {
                                addUndoStackElement(UndoRedoType.RichText(editOperation))
                            }

                            UndoStackOp.Remove -> textAndDrawViewModel.run {
                                removeUndoStackElement(UndoRedoType.RichText(editOperation))
                            }
                        }
                    }
                }

                launch {
                    suniaDrawViewModel.undoRedoOperationShared.collect {
                        textAndDrawViewModel.run {
                            addUndoStackElement(UndoRedoType.Draw(it))
                        }
                    }
                }
            }
        }

        /**
         * 记录文本全新操作。比如文本变化，插入图片，绘制笔画
         *
         * 注：undo或者redo导致的文本变化，插入图片，绘制笔画等，不算是CreatedOp
         */
        private fun addUndoStackElement(undoRedoType: UndoRedoType) {
            mUndoStack.add(undoRedoType)
            mRedoStack.clear()
            updateUndoAndRedoState()
        }

        /**
         * 更新状态
         */
        private fun updateUndoAndRedoState() {
            textAndDrawViewModel.canUndo = mUndoStack.isNotEmpty()
            textAndDrawViewModel.canRedo = mRedoStack.isNotEmpty()
        }

        /**
         * 记录文本全新操作。比如文本变化，插入图片，绘制笔画
         *
         * 注：undo或者redo导致的文本变化，插入图片，绘制笔画等，不算是CreatedOp
         */
        private fun removeUndoStackElement(undoRedoType: UndoRedoType) {
            mUndoStack.removeLast()
            updateUndoAndRedoState()
        }

        /**
         * 获取undo的类型
         */
        private fun runUndoAndGetType(): UndoRedoType? {
            if (mUndoStack.isEmpty()) {
                return null
            }
            val undoRedoType = mUndoStack.removeLast()
            mRedoStack.add(undoRedoType)
            updateUndoAndRedoState()
            return undoRedoType
        }

        /**
         * 获取redo的类型
         */
        private fun runRedoAndGetType(): UndoRedoType? {
            if (mRedoStack.isEmpty()) {
                return null
            }
            val undoRedoType = mRedoStack.removeLast()
            mUndoStack.add(undoRedoType)
            updateUndoAndRedoState()
            return undoRedoType
        }

        fun undo() {
            val undoType = runUndoAndGetType()
            when (undoType) {
                is UndoRedoType.Draw -> suniaDrawViewModel.undo()
                is UndoRedoType.RichText -> richTextViewModel.undo()
                null -> {}
            }
        }

        fun redo() {
            val redoType = runRedoAndGetType()
            when (redoType) {
                is UndoRedoType.Draw -> suniaDrawViewModel.redo()
                is UndoRedoType.RichText -> richTextViewModel.redo()
                null -> {}
            }
        }
    }
}