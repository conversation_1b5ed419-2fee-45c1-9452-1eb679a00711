package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.infiniteMeshBackground
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toComposeColor
import com.tcl.ai.note.utils.toPx
import kotlin.math.absoluteValue

const val MESH_CELLS = 22f

/**
 * 编辑框网格背景
 */
@Composable
fun MeshStyle(
    darkTheme: Boolean = isSystemInDarkTheme(),
    modifier:Modifier = Modifier,
    width:Dp =0.dp,
    offsetY:Float =0f,
    getTranslation: () -> FloatArray,
    bgMode: BgMode = BgMode.none,
    bgColor: Long,
    lineSpaceDp: Float = Skin.lineHeight().toFloat(),
) {
    val configuration = LocalConfiguration.current
    val screenHeightDp = configuration.screenHeightDp
    val screenWidthDp = configuration.screenWidthDp
    val height: Dp = (screenHeightDp + lineSpaceDp).dp

    Box(
        modifier = modifier
            .width(width)
            .background(color =(darkTheme ).judge(
                bgColor.toComposeColor().inverseColor() ,
                bgColor.toComposeColor()
            ))
            .graphicsLayer {
                val offset = getTranslation()
                val  moveX = offset[0]
                val offY = offset[1]
                val lineSpacePx = (lineSpaceDp).dp.toPx
                val widthPx = width.toPx
                val screenWidthPx = screenWidthDp.dp2px
                val offX = (widthPx > screenWidthPx).judge(
                    moveX,
                    0f
                )
                if(offY.absoluteValue >= (offsetY+lineSpacePx*2)){
                    val offY1 = (offY.absoluteValue - (offsetY+lineSpacePx*2)) % lineSpacePx
                    translationY = -(offsetY + offY1+lineSpacePx)
                }else{
                    translationY = offY
                }
                translationX = offX


            }

    ) {

        Box(
            modifier = Modifier
                .width(width)
                .infiniteMeshBackground(
                    bgMode = bgMode,
                    paddingTop = offsetY.toInt().px2dp.dp,
                    lineColor = (darkTheme).judge(
                        Color(0x26FFFFFF),
                        Color(0x1A000000),

                    ),
                    screenWidthDp = screenWidthDp,
                    width = width,
                    lineSpace = lineSpaceDp.dp,
                    height = height.toPx,
                )
        )
    }
}


