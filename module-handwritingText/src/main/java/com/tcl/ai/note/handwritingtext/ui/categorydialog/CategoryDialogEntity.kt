package com.tcl.ai.note.handwritingtext.ui.categorydialog

import androidx.compose.ui.graphics.Color

// 弹窗类型枚举
enum class CategoryDialogType {
    NEW_CATEGORY,        // 新建分类
    EDIT_CATEGORY,       // 编辑分类
    MOVE_NOTE_TO_CATEGORY // 移动笔记到分类
}
// 数据模型
data class DialogCategory(
    val categoryId: Long,
    val name: String,
    val colorIndex:Int,
    val color: Color?,
    val icon:Int?,
    val noteCounts: Int = 0, // 新增字段，表示分类下的笔记数量
)

data class DialogNote(
    val id: Long,
    val name: String,
    val content: String,
    val categoryId: Long,
)
// 弹窗状态数据类
data class CategoryDialogState(
    val isPreviewMode: Boolean = false, //是否是在富文本编辑页触发移动
    val isVisible: Boolean = false,
    val isMoveToNewCategory: Boolean = false,//是否是移动新建目录
    val selectedCategoryId:Long = 1L,
    val noteCategories: List<DialogCategory> = emptyList(),
    val type: CategoryDialogType = CategoryDialogType.NEW_CATEGORY,
    val editingDialogCategory: DialogCategory? = null,
    val dialogNoteToMove: DialogNote? = null,           // 要移动的笔记
    val notesToMove: List<DialogNote> = emptyList() // 要批量移动的笔记列表
)