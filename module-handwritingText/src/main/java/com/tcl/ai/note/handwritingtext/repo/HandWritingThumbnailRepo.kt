package com.tcl.ai.note.handwritingtext.repo

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Point
import android.graphics.Rect
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.util.fastForEach
import androidx.core.net.toUri
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.sunia.penengine.sdk.operate.canvas.DarkModeMenu
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.DrawPoint
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.handwritingtext.database.entity.FirstScreenThumbnailType
import com.tcl.ai.note.handwritingtext.sunia.thumbnail.SuniaThumbnailUtil
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.clear
import com.tcl.ai.note.utils.getData
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.putData
import com.tcl.ai.note.utils.removeData
import com.tcl.ai.note.utils.screenSizeMax
import com.tcl.ai.note.utils.screenSizeMin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * 获取手绘图的缩略图
 *
 */
object HandWritingThumbnailRepo {
    private const val TAG = "HandWritingThumbnailRepo"
    init {
        GlobalContext.applicationScope.launch(Dispatchers.IO) {
            val map = HandWritingThumbnailRecoveryDataStore.getAllData()
            Logger.i(TAG, "Recovery-HandWritingThumbnail Job: $map")
            map.keys.forEach {
                val noteId = it.name.toLongOrNull()
                val thumbnailPath = map[it]
                Logger.i(TAG, "Recovery-HandWritingThumbnail, noteId: $noteId, thumbnailPath: $thumbnailPath")
                if (noteId != null && !thumbnailPath.isNullOrEmpty()){
                    // 上次进程被杀了，缩略图没有生成。重新生成
                    listOf(
                        async { saveBitmap(noteId, false) },
                        async { saveBitmap(noteId, true) },
                    ).awaitAll()
                    // 通知首页更新
                    val note = NoteRepository2.get(noteId)
                    Logger.v(TAG, "Recovery-HandWritingThumbnail, notify update thumbnail, handwritingThumbnail: ${note?.handwritingThumbnail}")
                    note?.handwritingThumbnail?.let {
                        NoteRepository2.updateThumbnail(noteId, it)
                    }
                }
            }
        }

    }

    private fun getFilePath(nodeId: Long, isDark: Boolean = false): String {
        val filesDir = File(nodeId.idToEntPath()).parentFile!!
        if (!filesDir.exists()) {
            filesDir.mkdir()
        }
        val darkSuffix = if (isDark) "_dark" else ""
        val fileName = "handwriting_thumbnails_noteId_$nodeId$darkSuffix.png"
        return filesDir.absolutePath + "/$fileName"
    }

    /**
     * 记录缩略图预保存,避免进程被杀死无法正常输出缩略图
     */
    suspend fun recordNeedSaveBitmap(noteId: Long?) {
        if (noteId == null) {
            return
        }
        val bitmapFile = File(getFilePath(noteId, false))
        HandWritingThumbnailRecoveryDataStore.putData(noteId.toString(), bitmapFile.absolutePath)
        val darkBitmapFile = File(getFilePath(noteId, true))
        HandWritingThumbnailRecoveryDataStore.putData(noteId.toString(), darkBitmapFile.absolutePath)
    }

    suspend fun saveBitmap(nodeId: Long, isDark: Boolean = false) =
        withContext(Dispatchers.IO) {
            try {
                val file = File(getFilePath(nodeId, isDark))
                HandWritingThumbnailRecoveryDataStore.putData(nodeId.toString(), file.absolutePath)

                val thumbnailSaveScale = 0.3f
                val width = (screenSizeMin * thumbnailSaveScale).roundToInt()
                val height = (screenSizeMax * thumbnailSaveScale).roundToInt()

                val visibleSize = Rect(
                    0,
                    // contentRange.top,
                    0,
                    screenSizeMin,
                    // 底部越界不会有影响，根据宽度缩放的
                    // contentRange.top + screenSizeMax * 2,
                    screenSizeMax,
                )

                val exportSize = Rect(
                    0,
                    0,
                    width,
                    height,
                )

                val outPath = SuniaThumbnailUtil.saveOfflineThumbnail(
                    file.parent!!,
                    file.name,
                    visibleSize,
                    exportSize,
                    if (isDark) DarkModeMenu.MODE_HSL else DarkModeMenu.NONE
                )
                HandWritingThumbnailRecoveryDataStore.removeData(nodeId.toString(), file.absolutePath)
                Logger.d(TAG, "saveBitmap, nodeId: $nodeId, isDark: $isDark, outPath: $outPath")
            } catch (e: Exception) {
                Logger.w(TAG, "getIconFromNet failed: ${e.stackTraceToString()}")
            }
        }

    suspend fun deleteHandwritingThumbnail(nodeId: Long) = withContext(Dispatchers.IO) {
        try {
            val file = File(getFilePath(nodeId))
            if (file.exists()) {
                file.delete()
            }
            val fileDark = File(getFilePath(nodeId, true))
            if (fileDark.exists()) {
                fileDark.delete()
            }
        } catch (e: Exception) {
            Logger.w(TAG, "getIconFromNet failed: ${e.stackTraceToString()}")
        }
    }

    fun getBitmapPath(noteId: Long, isDark: Boolean = false): String? {
        // 最新版本缩略图存放位置
        val path = getFilePath(noteId, isDark)
        val file = File(path)
        if (file.exists()) {
            Logger.v(TAG, "getBitmapPath, version: 2.0, noteId: $noteId, isDark: $isDark, path: $path")
            return file.toUri().toString()
        }
        // v1.0版本的缩略图存放位置
        val darkSuffix = if (isDark) "_dark" else ""
        val fileName = "handwriting_thumbnails_noteId_$noteId$darkSuffix.png"
        val thumbnailVersion1SavePath = File(GlobalContext.instance.filesDir.absolutePath + "/HandWritingThumbnails/$fileName")
        if (thumbnailVersion1SavePath.exists()) {
            Logger.v(TAG, "getBitmapPath, version: 1.0, noteId: $noteId, isDark: $isDark, path: $path")
            return thumbnailVersion1SavePath.toUri().toString()
        }
        return null
    }
}

object HandWritingThumbnailRecoveryDataStore {
    // 创建DataStore
    private val Context.HandWritingThumbnailRecoveryDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "HandWritingThumbnailRecovery"
    )

    // DataStore变量
    private val dataStore: DataStore<Preferences> by lazy {
        GlobalContext.instance.HandWritingThumbnailRecoveryDataStore
    }

    suspend fun putData(key: String, value: String) {
        dataStore.putData(key, value)
    }

    suspend fun removeData(key: String, value: String) {
        dataStore.removeData(key, value)
    }

    suspend fun getAllData(): Map<Preferences.Key<String>, String> {
        return dataStore.data.first().asMap() as Map<Preferences.Key<String>, String>
    }

    suspend fun clear() {
        dataStore.clear()
    }
}