package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.voicetotext.util.generateAudioPath
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.util.getAudioFileFolder
import com.tcl.ai.note.voicetotext.view.widget.TabletTopAudioBlock
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import java.io.File
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.attribute.BasicFileAttributes


@Composable
internal fun TopAudioBar(
    richTextViewModel: RichTextViewModel2 = hiltViewModel(),
    recordingViewModel: RecordingViewModel = hiltViewModel(),
    audioToTextViewModel: AudioToTextViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    val recordPlayingState by audioToTextViewModel.recordPlayingState.collectAsState()
    val state by richTextViewModel.uiState.collectAsState()
    val audioList by remember(state) { mutableStateOf(state.audios) }
    val showAudioPanel by audioToTextViewModel.topAudioVisibleState.collectAsState()

    val filteredAudioList = audioList.filter {
        if (it.audioDuration == 0L) {
            getAudioDuration(it.audioPath) >= 2000L
        } else {
            it.audioDuration >= 2000L
        }
    }

    val sortedAudioPairs = filteredAudioList.map {
        it.audioPath to it.audioDuration
    }.sortedBy { (audioPath, _) ->
        // 按文件创建时间排序，时间早的在前
        try {
            val file = File(audioPath)
            if (file.exists()) {
                val path: Path = file.toPath()
                val attr: BasicFileAttributes = Files.readAttributes(path, BasicFileAttributes::class.java)
                attr.creationTime().toMillis()
            } else {
                Long.MAX_VALUE // 文件不存在的放到最后
            }
        } catch (e: Exception) {
            Long.MAX_VALUE // 出错的放到最后
        }
    }

    LaunchedEffect(audioList) {
        coroutineScope.launchIO {
            if (audioList.isNotEmpty()) {
                val keepAudioList = audioList.map { it.audioPath }
                getAudioFileFolder(state.noteId!!).listFiles()?.forEach { file ->
                    if (file.isFile && !keepAudioList.contains(file.absolutePath)) {
                        file.delete()
                    }
                }
            }
        }
    }

    TabletTopAudioBlock(
        noteId = state.noteId ?: 0L,
        audioPaths = sortedAudioPairs.map { it.first },
        audioDurations = sortedAudioPairs.map { it.second },
        showPanel = showAudioPanel,
        recordPlayingState = recordPlayingState,
        onSeek = { position, audioPath ->
            // 禁止拖动：不执行任何操作
            audioToTextViewModel.seekTo(position, audioPath)
        },
        onPlayClick = { audioPath ->
            audioToTextViewModel.playAudio(audioPath)
        },
        onPauseClick = {
            audioToTextViewModel.pausePlay()
        },
        onAudioToTextClick = {

        },
        onConfirmRename = { oldAudioPath, newAudioPath ->
            Logger.d("TopAudioBar", "onConfirmRename: $oldAudioPath -> $newAudioPath")
            richTextViewModel.onRenameAudio(
                oldAudioPath = oldAudioPath,
                newAudioPath = newAudioPath
            )
        },
        onDeleteAudios = { deletedAudioPaths ->
            deletedAudioPaths.forEach { deletedAudioPath ->
                audioToTextViewModel.deleteAudioFile(deletedAudioPath)
                richTextViewModel.onDeleteAudio(deletedAudioPath)
            }
        },
        onStartRecordingClick = {
            // 开始录音
            val audioPath = generateAudioPath(noteId = state.noteId ?: 0L)
//            recordingViewModel.recordingIntent(RecordIntent.StartRecord(audioPath))
            val newList = audioList.toMutableList()
            // 添加到列表中，不需要指定位置，保存后会按创建时间排序
            newList.add(EditorContent.AudioBlock(audioPath = audioPath,getAudioDuration(audioPath)))
            richTextViewModel.onAudiosChanged(newList)
            audioToTextViewModel.updateTopAudioBarState(false)
        },
        recordingViewModel = recordingViewModel,
        audioToTextViewModel = audioToTextViewModel
    )
}