package com.tcl.ai.note.handwritingtext.vm.pen

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.ColorSourceAnnotation
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.bean.PenStyle
import com.tcl.ai.note.handwritingtext.bean.copyWith
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isFridaPhone
import com.tcl.ai.note.utils.toComposeColor
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject

/**
 * 顶部笔刷栏数据管理viewmodel
 */
class PenToolbarViewModel : ViewModel(){





    private val _pensStyles = mutableStateListOf<PenStyle>().apply {
        if(isFridaPhone){
            add(PenStyle.PenFountain())
            add(PenStyle.Pencil())
        }else{
            add(PenStyle.PenFountain())
            add(PenStyle.BallPen())
            add(PenStyle.Pencil())
            add(PenStyle.MarkerPen())
        }


    }
    val penStyles: List<PenStyle> get() = _pensStyles.toList()




    private val defColos = listOf(
        PenColor(Color(0xFF000000)),
        PenColor(Color(0xFFFFFFFF)),
        PenColor(Color(0xFF0243C8)),
        PenColor(Color(0xFF7EC443)),
        PenColor(Color(0xFFF8EB03)),
        PenColor(Color(0xFFFE2C1C)),
        PenColor(Color(0xFFBF35F8))
    )

    private val _alternativeColors = MutableStateFlow<List<PenColor>>(emptyList())
    val alternativeColors: StateFlow<List<PenColor>> = _alternativeColors.asStateFlow()

    // 新增笔刷选择状态Flow，用于埋点监听
    private val _penSelectedIndexFlow = MutableStateFlow(0)
    val penSelectedIndexFlow: StateFlow<Int> = _penSelectedIndexFlow.asStateFlow()

    var penSelectedIndex by mutableIntStateOf(0)
        private set
    var initCount by mutableIntStateOf(0)
        private set

    val maxInitCount = 2
    val isInit: Boolean
        get() = initCount ==maxInitCount

    val selectedPen: PenStyle
        get() = penStyles.getOrNull(penSelectedIndex)?:penStyles[0]


    fun selectPen(index: Int) {
        penSelectedIndex = index
        _penSelectedIndexFlow.value = index
        saveSelectedIndex()
    }

    private val maxRecentColors:Int = 7



     fun collectColor(penColor: PenColor) {
        val list = _alternativeColors.value.toMutableList()
        list.remove(penColor)
        list.add(0, penColor)
        if (list.size > maxRecentColors) {
            list.removeAt(list.size - 1)
        }
        _alternativeColors.value = list
         saveAlternativeColors()
    }


    /**
     * 保存最近使用的笔刷
     */
    private fun saveSelectedIndex() {
        viewModelScope.launch {
            AppDataStore.putInt(::penSelectedIndex.name, penSelectedIndex)
        }
    }
    private fun loadSelectedIndex() {
        viewModelScope.launch {
            penSelectedIndex = AppDataStore.getInt(::penSelectedIndex.name, 0)
            _penSelectedIndexFlow.value = penSelectedIndex
            initCount++
        }
    }

    /**
     * 保存最近使用的颜色
     */
    private fun saveAlternativeColors() {
        viewModelScope.launch {
            val json = JSONArray().apply {
                alternativeColors.value.forEach { penColor ->
                    val obj = JSONObject().apply {
                        put(penColor::color.name, penColor.color.toArgb())
                        put(penColor::alpha.name, penColor.alpha)
                    }
                    put(obj)
                }
            }.toString()
            AppDataStore.putStringData(::alternativeColors.name, json)
        }
    }

    /**
     * 加载最近使用的颜色
     */
    private fun loadAlternativeColors() {
        viewModelScope.launch {
            val json = AppDataStore.getStringData(::alternativeColors.name, "")
            val colors = if (json.isNotEmpty()) {
                JSONArray(json).run {
                    List(length()) { i ->
                        val obj = getJSONObject(i)
                        PenColor(
                            color = obj.getLong(PenColor::color.name).toComposeColor(),
                            alpha = obj.getInt(PenColor::alpha.name)
                        )
                    }
                }
            } else {
                defColos
            }
            _alternativeColors.value = colors
        }
    }

    /**
     * 保存笔刷样式
     */
    private fun savePenStyle() {
        viewModelScope.launch {
            val json = JSONArray().apply {
                _pensStyles.forEach { penStyle ->
                    val obj = JSONObject().apply {
                        put(penStyle::width.name, penStyle.width)
                        put(penStyle::color.name, penStyle.color.toArgb())
                        put(penStyle::alpha.name, penStyle.alpha)
                        put(penStyle::curProgress.name, penStyle.curProgress)
                    }
                    put(obj)
                }
            }.toString()
            AppDataStore.putStringData(::penStyles.name, json)

        }
    }

    /**
     * 加载笔刷样式
     */
    private fun loadPenStyle() {
        viewModelScope.launch {
            val json = AppDataStore.getStringData(::penStyles.name, "")
            if (json.isNotEmpty()) {
                JSONArray(json).run {
                    List(length()) { i ->
                        val obj = getJSONObject(i)
                        if(i<_pensStyles.size){
                            val penStyle = _pensStyles[i]
                            _pensStyles[i] = penStyle.copyWith(
                                width = obj.optString(PenStyle::width.name,"1").toFloat(),
                                color = obj.optLong(PenStyle::color.name).toComposeColor(),
                                alpha = obj.optInt(PenStyle::alpha.name),
                                curProgress = obj.optInt(PenStyle::curProgress.name,1)
                            )
                        }


                    }
                }
            }
            initCount++
        }
    }

    /**
     * @param isColorFromInternal false  表示来自外部颜色设置，菜单栏色盘
     */
    fun updateSelectedPen(width: Float? = null, color: Color? = null,alpha: Int? = null,curProgress: Int? =null) {
        Logger.d(TAG, "updateSelectedPen: $width, $color, $alpha,$curProgress")

        penStyles.getOrNull(penSelectedIndex)?.let {
            _pensStyles[penSelectedIndex] = it.copyWith(width =width, color= color, alpha = alpha,curProgress = curProgress)
            savePenStyle()
        }


    }

    init {
        loadAlternativeColors()
        loadPenStyle()
        loadSelectedIndex()
    }


    companion object{
        private const val TAG = "PenToolbarViewModel"
    }

}