package com.tcl.ai.note.handwritingtext.ui.richtext

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Point
import android.graphics.PointF
import android.graphics.RectF
import android.text.Editable
import android.util.Size
import android.view.MotionEvent
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.MainThread
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.event.AIEventType
import com.tcl.ai.note.event.AIReplaceEvent
import com.tcl.ai.note.handwritingtext.richtext.converter.RichTextStyleEntityToSpanConverter
import com.tcl.ai.note.handwritingtext.richtext.converter.applyRichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.converter.toRichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.history.RichTextEditOperation
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorPointF
import com.tcl.ai.note.handwritingtext.utils.HtmlUtils
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.handwritingtext.vm.event.RichTextOperateEvent
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2.SelectionRange
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getImeHeight
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * RichTextController
 *
 * 富文本输入UI控件、状态、事件管理的控制器。
 * 负责中间层: viewModel <==> 富文本输入视图(AREditText) <==> 内容ViewGroup
 * 提供内容装载、撤销重做、富文本风格apply、光标与编辑态控制等方法。
 *
 * @param context 上下文对象
 * @param viewModel 关联的富文本VM
 * @param onContentChanged 富文本内容变更回调（plainText, RichTextStyleEntity）
 */
class RichTextController(
    private val context: Context,
    private val viewModel: RichTextViewModel2,
) {
    private val view = AREditText(context)
    private var viewHolder: RichTextViewHolder? = null
    private var isDestroyed = false
    val jobs = mutableListOf<Job>()

    // 手绘最后一笔的位置
//    var lastDrawRect: RectF? = null

    // 添加焦点请求的防抖机制
    private var lastFocusRequestTime = 0L
    private val FOCUS_REQUEST_DEBOUNCE_TIME = 300L

    // 输入超限提示
    var onLimitToast: (() -> Unit)? = null

    private var isKeyboardActive = false
    private var isPreviewMode = false

    // 预览模式下点击切换到编辑模式的回调
    var onPreviewModeClickToEdit: (() -> Unit)? = null

    /**
     * 添加富文本控件到指定的ViewGroup容器
     * @param viewGroup 要挂载的控件容器
     */
    fun attachViewGroup(viewGroup: ViewGroup) {
        if (isDestroyed) {
            Logger.w(TAG, "RichTextController is destroyed, cannot attach view")
            return
        }

        // 先清理旧的ViewHolder（防止多次attach导致资源泄露）
        viewHolder?.onRelease()

        viewHolder = RichTextViewHolder(context, view) { content, style ->
            viewModel.onRichTextChanged(content, style)
        }
        Logger.d("attachViewGroup : " + view.hashCode())
        viewGroup.addView(view)
    }

    fun dispatchTouchEvent(event: MotionEvent) = view.dispatchTouchEvent(event)


    /**
     * 销毁控制器，清理所有资源
     */
    fun destroy() {
        if (isDestroyed) return
        isDestroyed = true

        Logger.d(TAG, "Destroying RichTextController with AREditText: ${view.hashCode()}")

        jobs.forEach { it.cancel() }
        // 清理ViewHolder
        viewHolder?.onRelease()
        viewHolder = null
        setOnCursorToScreenListener(null)
        // 清理AREditText
        view.destroy()

        // 移除view
        (view.parent as? ViewGroup)?.removeView(view)
    }

    private fun implViewModelFunc() = with(viewModel) {
        // 实现RichTextViewModel2的getLineHeight函数
        view.implGetLineHeight = view::getLineHeight
        // 实现RichTextViewModel2的changeScale函数
        view.implChangeScale = view::changeScale
        // undo操作
        view.implUndoFunc = view::undo
        // redo操作
        view.implRedoFunc = view::redo
        view.implToHtml = {
            if (view.text.isNullOrBlank())
                null
            else
                HtmlUtils.toHtml(view.editableText)
        }
        view.implResetPasteFunc = {
            view.resetPaste()
        }
    }

    /**
     * 初始化监听和事件绑定
     */
    init {
        implViewModelFunc()
        view.setMaxContentLength(MAX_CONTENT_LENGTH)
        view.setUndoRedoStateListener(object : AREditText.UndoRedoStateListener{
            override fun onUndoRedoStateChanged(canUndo: Boolean, canRedo: Boolean) {
                Logger.d(TAG, "Undo/Redo state changed: canUndo=$canUndo, canRedo=$canRedo")
                RichTextEventManager.updateToolBarStyleState {
                    it.copy(
                        isCanUndo = canUndo,
                        isCanRedo = canRedo
                    )
                }
            }

            override fun onUndoStackAdded(newOperation: RichTextEditOperation) {
                viewModel.run { <EMAIL>(newOperation) }
            }

            override fun onUndoStackRemoved(newOperation: RichTextEditOperation) {
                viewModel.run { <EMAIL>(newOperation) }
            }
        })

        view.setOnLimitListener(object : AREditText.OnLimitListener {
            override fun onInputLimit() {
                onLimitToast?.invoke() // UI提示“内容已达最大字数”
            }
            override fun onPasteLimit() {
                onLimitToast?.invoke() // UI提示“粘贴超长已阻止”
            }
        })

        // 绑定选择改变事件（实时反馈给ViewModel）
        view.setonSelectionChanged { start: Int, end: Int ->
            viewModel.selectionRange = SelectionRange(start, end)
        }

        // 绑定测量变化（高度宽度改变）（反馈给ViewModel可用于布局/自适应等）
        view.setOnMeasureChanged { width: Int, height: Int ->
            Logger.d(TAG, "measureChanged: ${width}, ${height}")
            viewModel.richTextSize = Size(width, height)
        }

        // 监听请求焦点事件
        viewModel.viewModelScope.launch {
            viewModel.requestFocusEvent.collect {
                if (!isKeyboardActive) {
                    return@collect
                }
                // 防抖处理
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastFocusRequestTime > FOCUS_REQUEST_DEBOUNCE_TIME) {
                    lastFocusRequestTime = currentTime
                    // 确保在主线程中请求焦点
                    withContext(Dispatchers.Main) {
                        requestFocusAndShowKeyboard()
                    }
                }
            }
        }.apply {
            jobs.add(this)
        }

        // 富文本内容变化监听，触发内容回调
        view.setOnContentChangedListener {
            val style = view.toRichTextStyleEntity()
            viewModel.onRichTextChanged(view.text.toString(), style)

        }

        view.setOnCopyListener(object : AREditText.OnCopyListener  {
            override fun onCopy(start: Int, end: Int, text: CharSequence) {
                viewModel.startCopy(start, end, text)
            }

            override fun startPaste(
                editable: Editable?,
                start: Int,
                end: Int,
                before: Int,
                count: Int,
                isDelete: Boolean
            ) {
                viewModel.startPates {
                    view.handlePaste(editable, start, end, before, count, isDelete)
                }
            }
        })

        // 监听富文本操作类事件（撤销/重做/装载内容等）
        viewModel.viewModelScope.launch {
            RichTextEventManager.richTextOperateEvent.collect { event ->
                event?.let {
                    handleOperateEvent(it)
                }
            }
        }.apply {
            jobs.add(this)
        }

        // 动态追加N行换行到末尾
        viewModel.viewModelScope.launch {
            viewModel.insertLineBreakEvent
                .filter { it > 0 }
                .collectLatest { n ->
                    appendLineBreaksAtEnd(n)
                }
        }.apply {
            jobs.add(this)
        }

        // 监听富文本AI操作类事件（AI润色/AI帮写/手写转文本）
        viewModel.viewModelScope.launch {
            AIReplaceEvent.getAIReplaceEvent().collect{ replace->
                val text = replace.text
                if (text.isNotBlank()) {
                    withContext(Dispatchers.Main) {
                        when (replace) {
                            is AIEventType.AIPolishReplace -> {
                                // AI润色替换文本
                                view.setText(text)
                                // 光标移动到最后
                                view.post {
                                    val length = view.text?.length ?: 0
                                    view.setSelection(length)
                                }
                            }
                            else -> {
                                // 手写转文本与AI帮写
                                // 插入文本到光标处
                                view.insertTextAtCursor(text)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 请求焦点并显示键盘
     */
    private fun requestFocusAndShowKeyboard() {
        try {
            if (!view.hasFocus()) {
                view.requestFocusAndShowCursor(view.selectionStart, true)
            } else {
                // 如果是新建笔记，确保光标在开始位置
                if (viewModel.uiState.value.isNewNote && view.text?.isEmpty() == true) {
                    view.setSelection(0)
                }
            }
        } catch (ex: Exception) {
            Logger.e(TAG, "requestFocusAndShowKeyboard error: ${ex.message}")
        }
    }

    /**
     * 隐藏键盘并清除焦点
     */
    private fun hideKeyboardAndClearFocus() {
        try {
            view.hideKeyboardAndClearFocus()
        } catch (ex: Exception) {
            Logger.e(TAG, "hideKeyboardAndClearFocus error: ${ex.message}")
        }
    }

    /**
     * 响应operateEvent事件：如撤销、重做、内容装载等
     */
    private fun handleOperateEvent(operateEvent: RichTextOperateEvent) {
        when (operateEvent) {
            is RichTextOperateEvent.Undo -> {
                view.undo()
            }

            is RichTextOperateEvent.Redo -> {
                view.redo()
            }

            is RichTextOperateEvent.LoadNote -> {
                setNoteContent(operateEvent.content,operateEvent.richTextStyleEntity, operateEvent.init)
            }
        }
    }

    /**
     * 设置编辑区内容并渲染富文本（用于从ViewModel/content加载数据库恢复内容）
     * @param content 文本内容
     * @param richTextStyleEntity 富文本结构体
     * @param init 是否初始化文本
     */
    private fun setNoteContent(content:String, richTextStyleEntity: RichTextStyleEntity, init: Boolean = false) {
        viewModel.viewModelScope.launch {
            // 忽略选择改变事件，避免在设置内容时触发选择改变回调
            view.setIgnoreSelectionChanged(true)
            // 停止所有监听器，避免在设置内容时触发不必要的回调
            view.stopAllMonitor()
            withContext(Dispatchers.Main){
                if (content.isNotBlank()) {
                    Logger.d("setNoteContent: $content")
                    // 应用富文本样式并设置内容
                    view.applyRichTextStyleEntity(
                        richTextStyleEntity,
                        content
                    )
                } else {
                    // 如果内容为空，则设置空文本
                    view.setText("")
                }
                // 内容设置完成后，重新启动所有监听器
                view.startAllMonitor()
                // 设置undo初始状态
                if (init) {
                    view.initUndoState(view.text)
                    // 主动触发初始化底部工具状态
//                    view.onSelectionChanged(view.selectionStart, view.selectionEnd)
                }
            }
            // 恢复 selectionChanged 响应
            view.setIgnoreSelectionChanged(false)
        }.apply {
            jobs.add(this)
        }
    }

    fun updateBottomMargin(bottom: Int) {
        view.setMargins(bottom = bottom)
    }

    fun updateBottomMenuBarHeight(height: Int) {
        view.setBottomBarHeight(height)
    }

    // 监听光标回到屏幕内，导致的缩放平移
    fun setOnCursorToScreenListener(onCursorToScreenListener: ((translationX: Float, translationY: Float) -> Unit)?) {
        view.setOnCursorToScreenListener(onCursorToScreenListener)
    }

    /**
     * 监听光标拖动柄移动布局
     */
    fun setScrollByCursorHandleListener(
        onScrollByCursorHandleStart: ((translationX: Float, translationY: Float) -> Unit)?,
        onScrollByCursorHandleEnd: ((translationX: Float, translationY: Float) -> Unit)?,
    ) {
        view.onScrollByCursorHandleStart = onScrollByCursorHandleStart
        view.onScrollByCursorHandleEnd = onScrollByCursorHandleEnd
    }

    @MainThread
    fun changeEditable(editable: Boolean) {
        isKeyboardActive = editable
        Logger.v(TAG, "changeEditable: $editable")
        // 自动恢复光标位置
        try {
            if (editable) {
                // 如果是切换到编辑模式，立即请求焦点和显示键盘
                requestFocusAndShowKeyboard()
            } else {
                // 切换到手绘模式时隐藏键盘和清除焦点
                hideKeyboardAndClearFocus()
            }
        } catch (ex: Exception) {
            Logger.e(TAG, "changeEditable error: ${ex.message}")
        }
    }

    /**
     * 预览模式下的编辑设置：允许接收点击事件但不主动请求焦点
     */
    @SuppressLint("ClickableViewAccessibility")
    @MainThread
    fun changeEditableForPreview(editable: Boolean) {
        isKeyboardActive = false // 预览模式下键盘不活跃
        isPreviewMode = editable
        Logger.v(TAG, "changeEditableForPreview: $editable")
        // 预览模式下不主动请求焦点和显示键盘，等待用户点击
        try {
            if (editable) {
                // 预览模式下，先清除当前焦点并隐藏键盘
                hideKeyboardAndClearFocus()
                if(!viewModel.uiState.value.isTopPopupShowing){
                    // 设置为可获取焦点，但不主动请求焦点
                    view.setFocusable(true)
                    view.setFocusableInTouchMode(true)
                }else{
                    viewModel.uiState.value.isTopPopupShowing = false
                }

                // 设置点击监听，当用户点击时切换到编辑模式
                view.setOnTouchListener { _, event ->
                    if (event.action == android.view.MotionEvent.ACTION_UP && isPreviewMode) {
                        // 用户点击了富文本区域，切换到编辑模式
                        onPreviewModeClickToEdit?.invoke()
                        false // 让事件继续传递，以便正常处理光标定位
                    } else {
                        false
                    }
                }
            } else {
                // 如果禁用编辑，隐藏键盘和清除焦点
                hideKeyboardAndClearFocus()
                view.setOnTouchListener(null)
            }
        } catch (ex: Exception) {
            Logger.e(TAG, "changeEditableForPreview error: ${ex.message}")
        }
    }

    /**
     * 手绘切换到键盘模式,重新定位光标位置
     */
//    private fun getCursorPosition() = try {
//        // 光标位置
//        val textLen = view.text?.length ?: 0
//        val start = viewModel.selectionRange.start.coerceIn(0, textLen)
//        // 手绘切换到键盘模式时，键盘光标位置为手绘内容下最近的光标可插入行
//        lastDrawRect?.let {
//            val lineHeight = view.lineHeight
//            val line = it.bottom.toInt() / lineHeight.toInt()
//            val index = view.layout.getLineEnd(line)
//            index
//        } ?: start
//    } catch (ex: Exception) {
//        Logger.e(TAG, "getCursorPosition error: ${ex.javaClass}, ${ex.message}")
//        0
//    }

    /**
     * 设置光标位置
     */
    fun setCursorIndexByPoint(point: PointF) {
        val cursorIndex = view.getOffsetForPosition(point.x, point.y)
        view.setSelection(cursorIndex)
        Logger.i(TAG, "setCursorIndexByPoint: $cursorIndex")
    }

    /**
     * 追加换行符到点击区域
     */
    fun addNewLineByPoint(point: PointF) {
        view.stopApplyMonitor()
        view.addNewLineByPoint(point)
        view.startApplyMonitor()
    }

    /**
     * 追加指定行数的换行到末尾。
     */
    private fun appendLineBreaksAtEnd(count: Int) {
        if (count <= 0) return
        val text = view.text
        if (text != null && text.length + count >= MAX_CONTENT_LENGTH)
            return
        // 记录当前光标位置
        val currentSelection = view.selectionStart
        val wasEmpty = view.text?.isEmpty()

        // 临时停止监听，防止触发不必要的回调
        view.stopAllMonitor()

        // 添加换行
        view.append("\n".repeat(count))

        if (wasEmpty == true) {
            // 如果空内容笔记，将光标设置到开始位置
            view.setSelection(0)
        }

        // 手写模式下，页面往下拉，view重新measure会触发此方法调用，导致软键盘弹出
        if (isKeyboardActive) {
            // 如果是新建笔记或者原来是空文本，将光标设置到开始位置
            if (viewModel.uiState.value.isNewNote || wasEmpty == true) {
                view.setSelection(0)

                // 延迟请求焦点，确保光标正确显示
                view.post {
                    if (!view.hasFocus()) {
                        view.requestFocusAndShowCursor()
                    } else {
                        // 如果已有焦点，重新设置光标位置
                        view.setSelection(0)
                    }
                }
            } else {
                // 对于现有内容，恢复原来的光标位置
                val newSelection = view.text?.let { minOf(currentSelection, it.length) }
                if (newSelection != null) {
                    view.setSelection(newSelection)
                }
            }
        }

        // 重新启动监听
        view.startAllMonitor()
    }

    /**
     * 检查键盘是否处于活跃状态
     */
    fun isKeyboardActive(): Boolean {
        return this.isKeyboardActive
    }

    /**
     * 检查键盘是否实际可见
     */
    fun isKeyboardVisible(): Boolean {
        // 使用IME高度来判断键盘是否真正可见
        val imeHeight = view.getImeHeight()
        return view.hasFocus() && this.isKeyboardActive && imeHeight > 0
    }

    /**
     * 检查富文本是否有焦点
     */
    fun hasFocus(): Boolean {
        return view.hasFocus()
    }

    /**
     * 获取当前光标位置
     */
    fun getCurrentCursorPosition(): Int {
        return view.selectionStart
    }

    /**
     * 设置view的高度
     */
    fun updateLayoutParams(block: (ViewGroup.LayoutParams) -> ViewGroup.LayoutParams) {
        view.layoutParams = block(view.layoutParams)
    }

    /**
     * 恢复编辑状态（锁屏解锁后调用）
     */
    fun restoreEditState(cursorPosition: Int, shouldShowKeyboard: Boolean) {
        try {
            Logger.d(TAG, "Restoring edit state with cursor position: $cursorPosition, showKeyboard: $shouldShowKeyboard")

            // 确保在主线程执行
            view.post {
                try {
                    // 请求焦点
                    view.setFocusable(true)
                    view.setFocusableInTouchMode(true)
                    view.requestFocus()

                    // 恢复光标位置
                    val textLength = view.text?.length ?: 0
                    val targetPosition = minOf(maxOf(0, cursorPosition), textLength)
                    view.setSelection(targetPosition)

                    // 根据保存的状态决定是否显示键盘
                    if (shouldShowKeyboard) {
                        // 重新设置为可编辑状态并显示键盘
                        <EMAIL> = true
                        view.showKeyboard()
                        Logger.d(TAG, "Edit state restored with keyboard visible")
                    } else {
                        // 只恢复焦点，不显示键盘
                        <EMAIL> = false
                        Logger.d(TAG, "Edit state restored with keyboard hidden")
                    }

                } catch (e: Exception) {
                    Logger.e(TAG, "Failed to restore edit state: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "restoreEditState error: ${e.message}")
        }
    }

    companion object {
        private const val TAG = "RichTextController"
        // 富文本最大输入长度
        const val MAX_CONTENT_LENGTH = 20000
    }
}