{"formatVersion": 1, "database": {"version": 2, "identityHash": "e3bdaef747219b0ad50c68ac0a5294d0", "entities": [{"tableName": "notes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`noteId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL, `contents` BLOB NOT NULL, `summary` TEXT, `first_picture` TEXT, `handwriting_thumbnail` TEXT, `hasAudio` INTEGER, `categoryId` INTEGER NOT NULL, `createTime` INTEGER, `modifyTime` INTEGER, `bgMode` TEXT NOT NULL, `bgColor` INTEGER NOT NULL, `deleteFlag` INTEGER NOT NULL, `isShow` INTEGER NOT NULL, `lastViewOffsetX` REAL NOT NULL, `lastViewOffsetY` REAL NOT NULL, `lastViewScale` REAL NOT NULL)", "fields": [{"fieldPath": "noteId", "columnName": "noteId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contents", "columnName": "contents", "affinity": "BLOB", "notNull": true}, {"fieldPath": "summary", "columnName": "summary", "affinity": "TEXT", "notNull": false}, {"fieldPath": "firstPicture", "columnName": "first_picture", "affinity": "TEXT", "notNull": false}, {"fieldPath": "handwritingThumbnail", "columnName": "handwriting_thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "hasAudio", "columnName": "hasAudio", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "categoryId", "columnName": "categoryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "modifyTime", "columnName": "modifyTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bgMode", "columnName": "bgMode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bgColor", "columnName": "bgColor", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleteFlag", "columnName": "deleteFlag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isShow", "columnName": "isShow", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastViewOffsetX", "columnName": "lastViewOffsetX", "affinity": "REAL", "notNull": true}, {"fieldPath": "lastViewOffsetY", "columnName": "lastViewOffsetY", "affinity": "REAL", "notNull": true}, {"fieldPath": "lastViewScale", "columnName": "lastViewScale", "affinity": "REAL", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["noteId"]}, "indices": [], "foreignKeys": []}, {"tableName": "contents", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `noteId` INTEGER NOT NULL, `type` TEXT NOT NULL, `content_data` TEXT NOT NULL, `styles` TEXT NOT NULL, `order` INTEGER NOT NULL, FOREIGN KEY(`noteId`) REFERENCES `notes`(`noteId`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "noteId", "columnName": "noteId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contentData", "columnName": "content_data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "styles", "columnName": "styles", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_contents_noteId", "unique": false, "columnNames": ["noteId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_contents_noteId` ON `${TABLE_NAME}` (`noteId`)"}], "foreignKeys": [{"table": "notes", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["noteId"], "referencedColumns": ["noteId"]}]}, {"tableName": "categories", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`categoryId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `colorIndex` INTEGER NOT NULL, `isRename` INTEGER NOT NULL, `createTime` INTEGER, `modifyTime` INTEGER)", "fields": [{"fieldPath": "categoryId", "columnName": "categoryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "colorIndex", "columnName": "colorIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is<PERSON>ename", "columnName": "is<PERSON>ename", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "modifyTime", "columnName": "modifyTime", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["categoryId"]}, "indices": [], "foreignKeys": []}, {"tableName": "draws", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `noteId` INTEGER NOT NULL, `strokes` BLOB NOT NULL, `createTime` INTEGER NOT NULL, `modifyTime` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "noteId", "columnName": "noteId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "strokes", "columnName": "strokes", "affinity": "BLOB", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifyTime", "columnName": "modifyTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_draws_noteId", "unique": true, "columnNames": ["noteId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_draws_noteId` ON `${TABLE_NAME}` (`noteId`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e3bdaef747219b0ad50c68ac0a5294d0')"]}}