import java.io.FileInputStream
import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.google.dagger.hilt)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.google.devtools.ksp)
    id("com.tct.sign-plugin")
}
// Read the version.txt file
val versionPropsFile = file("$rootDir/version.txt")
val versionProps = Properties().apply {
    load(FileInputStream(versionPropsFile))
}
android {
    namespace = "com.tcl.ai.note"
    compileSdk = libs.versions.compileSdk.get().toIntOrNull() ?: 35

    defaultConfig {
        applicationId = "com.tcl.ai.note2"
        minSdk = libs.versions.minSdk.get().toIntOrNull() ?: 34
        targetSdk = libs.versions.targetSdk.get().toIntOrNull() ?: 35
        versionCode = (versionProps["versionCode"]?.toString()?.toInt() ?: 1)
        versionName = versionProps["versionName"]?.toString() ?: "1.0.0"
        buildConfigField("Boolean", "IS_CN", "false")
        buildConfigField("boolean", "IS_PHONE", "true")
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
        ndk {
            abiFilters.add("arm64-v8a")
        }
    }
    buildFeatures {
        buildConfig = true
    }
    flavorDimensions += "region"
    flavorDimensions += "device"
    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    productFlavors {
        create("cn") {
            dimension = "region"
            versionNameSuffix = "-cn"
            buildConfigField("Boolean", "IS_CN", "true")
        }
        create("global") {
            dimension = "region"
            versionNameSuffix = "-global"
            buildConfigField("Boolean", "IS_CN", "false")
        }
        create("phone") {
            dimension = "device"
            versionNameSuffix = "-phone"
            buildConfigField("boolean", "IS_PHONE", "true")
        }
        create("tablet") {
            applicationId = "com.tcl.ai.note.hd"
            dimension = "device"
            versionNameSuffix = "-tablet"
            buildConfigField("boolean", "IS_PHONE", "false")
        }
    }

    sourceSets {

        getByName("phone") {
            java.srcDirs("src/phone/java")
            res.srcDirs("src/phone/res")
            manifest.srcFile("src/phone/AndroidManifest.xml")
        }
        getByName("tablet") {
            java.srcDirs("src/tablet/java")
            res.srcDirs("src/tablet/res")
            manifest.srcFile("src/tablet/AndroidManifest.xml")
        }
    }
    lint {
        checkDependencies = true
        abortOnError = false
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        compose = true
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
        jniLibs{
            pickFirsts.add("lib/x86/libc++_shared.so")
            pickFirsts.add("lib/x86_64/libc++_shared.so")
            pickFirsts.add("lib/armeabi-v7a/libc++_shared.so")
            pickFirsts.add("lib/arm64-v8a/libc++_shared.so")
        }
    }
}

//remoteSign {
//    //official version
//    tctProject = "TCL_GooglePlay_AAB_release"
//    tctKeyGroup = "tcl_release_new"
//    tctKeyType = "release"
//}

val moduleDashboardModule = project.findProperty("moduleDashboardModule")?.toString()?.toBoolean() ?: false
val handwritingTextModule = project.findProperty("handwritingTextModule")?.toString()?.toBoolean() ?: false
val handwritingToTextModule = project.findProperty("handwritingToTextModule")?.toString()?.toBoolean() ?: false
val textBeautifyModule = project.findProperty("textBeautifyModule")?.toString()?.toBoolean() ?: false
val voiceToTextModule = project.findProperty("voiceToTextModule")?.toString()?.toBoolean() ?: false
val summaryModule = project.findProperty("summaryModule")?.toString()?.toBoolean() ?: false
val polishModule = project.findProperty("polishModule")?.toString()?.toBoolean() ?: false
val helpWritingModule = project.findProperty("helpWritingModule")?.toString()?.toBoolean() ?: false
val moduleJournalModule = project.findProperty("moduleJournalModule")?.toString()?.toBoolean() ?: false


dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.material3)
    implementation(libs.androidx.adaptive)
    implementation(libs.androidx.lifecycle.service)
    implementation(libs.tcl.componentfrm)
    implementation(libs.androidx.appcompat)
    implementation(libs.dagger.hilt)
    ksp(libs.dagger.hilt.compiler)
    implementation(libs.androidx.lifecycle.viewmodel.compose)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)

    implementation(project(":module-base"))

    if(moduleDashboardModule){
        implementation(project(":module-dashboard"))
    }
    if(handwritingTextModule){
        implementation(project(":module-handwritingText"))
    }
    if(handwritingToTextModule){
        implementation(project(":module-handwritingToText"))
    }
    if(textBeautifyModule){
        implementation(project(":module-textbeautify"))
    }
    if(voiceToTextModule){
        implementation(project(":module-voiceToText"))
    }
    if(summaryModule){
        implementation(project(":module-summary"))
    }
    if(polishModule){
        implementation(project(":module-polish"))
    }
    if(helpWritingModule){
        implementation(project(":module-helpwriting"))
    }
    if(moduleJournalModule){
        implementation(project(":module-journal"))
    }
}